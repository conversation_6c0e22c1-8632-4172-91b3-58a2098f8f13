import { useState, useCallback, useEffect, useRef, lazy, Suspense } from 'react';
import { Button } from '@/components/ui/Button';
import { RefreshCw, TrendingUp, TrendingDown, BookOpen } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import type { PlotParams } from 'react-plotly.js';
import { useTheme } from '../components/theme-provider';
import { useNFTVerificationContext } from '../contexts/NFTVerificationContext';
import { useMobile } from '../hooks/useResponsiveDesign';
import { toast, toastError, toastSuccess } from '@/components/ui/use-toast';
import { useTranslation } from '@/hooks/useTranslation';

import { getTradingStatistics } from "@/api/trading.service";
import { getRealBalance } from "@/api/trading.service";
import DeactivatedAccountAlert from '@/components/DeactivatedAccountAlert';

import { useAutoTradingNotifications } from '../hooks/useAutoTradingNotifications';
import { getRiskSettings, saveRiskSettings } from "@/api/trading.service";
import { getAutoTradingStatus, toggleAutoTrading } from '../services/api';
import { Slider } from '@/components/ui/Slider';
import { usePaperTrading } from '../hooks/usePaperTrading';
import { paperTradingApi } from '../services/paperTradingApi';
import PaperTradingDashboard from '../components/PaperTradingDashboard';
import PaperTradingHelp, { PaperTradingTooltip } from '../components/PaperTradingHelp';
// Lazy load Plotly with Vite's dynamic import
const Plot = lazy(() => import('react-plotly.js').then((mod) => ({ default: mod.default })));

const DEFAULT_SYMBOL = 'BTCUSDT';
const DEFAULT_TIMEFRAME = '1h';

// Live BTC Price Component
function LiveBTCPrice() {
  const [priceData, setPriceData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [priceChange, setPriceChange] = useState<'up' | 'down' | 'same'>('same');
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'error' | 'loading'>('loading');
  const [errorCount, setErrorCount] = useState(0);

  useEffect(() => {
    // Import the price service
    import('../services/price.service').then(({ priceService }) => {
      // Subscribe to BTC price updates
      const unsubscribe = priceService.subscribe('BTCUSDT', (_symbol, newPriceData, prevPrice) => {
        setLoading(false);
        setConnectionStatus('connected');
        setErrorCount(0);

        // Track price changes for visual feedback
        if (prevPrice && newPriceData.price !== prevPrice) {
          if (newPriceData.price > prevPrice) {
            setPriceChange('up');
          } else if (newPriceData.price < prevPrice) {
            setPriceChange('down');
          } else {
            setPriceChange('same');
          }

          // Reset price change indicator after 1.5 seconds
          setTimeout(() => setPriceChange('same'), 1500);
        }

        setPriceData(newPriceData);
      });

      // Check connection status periodically
      const statusInterval = setInterval(() => {
        if (!priceService.getConnectionStatus()) {
          setConnectionStatus('error');
          setErrorCount(prev => prev + 1);
        }
      }, 5000);

      return () => {
        unsubscribe();
        clearInterval(statusInterval);
      };
    });
  }, []);



  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <span>{t('dashboard.btcPrice.loading')}</span>
      </div>
    );
  }

  if (!priceData) {
    return (
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <span>{t('dashboard.btcPrice.unavailable')}</span>
      </div>
    );
  }

  const changePercent = priceData.change_percent_24h || 0;
  const isPositive = changePercent >= 0;

  // Get price change color based on recent movement
  const getPriceChangeColor = () => {
    switch (priceChange) {
      case 'up': return 'text-green-600 bg-green-50 dark:bg-green-900/20';
      case 'down': return 'text-red-600 bg-red-50 dark:bg-red-900/20';
      default: return '';
    }
  };

  return (
    <div className="flex items-center space-x-3 text-sm">
      <span className="text-muted-foreground font-medium">BTC/USDT:</span>
      <div className={`flex items-center space-x-2 px-2 py-1 rounded-md transition-all duration-500 ${getPriceChangeColor()}`}>
        <span className="font-mono font-bold text-base">
          ${priceData.price?.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
          })}
        </span>
        {priceChange !== 'same' && (
          <div className="flex items-center">
            {priceChange === 'up' ? (
              <TrendingUp size={12} className="text-green-600 animate-pulse" />
            ) : (
              <TrendingDown size={12} className="text-red-600 animate-pulse" />
            )}
          </div>
        )}
      </div>
      {changePercent !== 0 && (
        <div className={`flex items-center space-x-1 ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
          {isPositive ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
          <span className="font-mono text-xs">
            {isPositive ? '+' : ''}{changePercent.toFixed(2)}%
          </span>
          <span className="text-xs text-muted-foreground">{t('dashboard.btcPrice.24h')}</span>
        </div>
      )}
      <div className="flex items-center space-x-1 text-xs text-muted-foreground">
        <div className={`w-2 h-2 rounded-full ${
          connectionStatus === 'loading' ? 'bg-yellow-400 animate-pulse' :
          connectionStatus === 'connected' ? 'bg-green-400' :
          'bg-red-400 animate-pulse'
        }`}></div>
        <span>
          {connectionStatus === 'loading' ? t('dashboard.btcPrice.connecting') :
           connectionStatus === 'connected' ? t('dashboard.btcPrice.live') :
           t('dashboard.btcPrice.error', { count: errorCount })}
        </span>
      </div>
    </div>
  );
}

interface ForecastData {
  symbol: string;
  timeframe: string;
  current_price: number;
  support_level: number;
  resistance_level: number;
  forecast: number[];
  forecast_dates: string[];
  chart_html: string;
  generated_at: string;
  [key: string]: any;
}

interface PlotlyData {
  data: PlotParams['data'];
  layout: Partial<PlotParams['layout']>;
}

export default function Dashboard() {
  const { t } = useTranslation();
  const { theme, isDark } = useTheme();
  const { isMobile } = useMobile();
  const [symbol] = useState(DEFAULT_SYMBOL);
  const [timeframe] = useState(DEFAULT_TIMEFRAME);

  // Paper trading hook
  const {
    isPaperMode,
    isLoading: paperTradingLoading,
    switchTradingMode,
    account: paperAccount,
    balance: paperBalance
  } = usePaperTrading();

  // Debug log for main Dashboard paper mode state
  console.log(`[Dashboard] Component render - isPaperMode: ${isPaperMode}`);

  // Force re-render key for mode-aware components
  const [modeChangeKey, setModeChangeKey] = useState(0);

  // Track mode changes and force re-renders
  useEffect(() => {
    console.log(`[Dashboard] Trading mode changed - isPaperMode: ${isPaperMode}`);
    // Force re-render of mode-aware components
    setModeChangeKey(prev => prev + 1);
  }, [isPaperMode]);

  // Help modal state
  const [showPaperTradingHelp, setShowPaperTradingHelp] = useState(false);

  // Auto-trading notifications
  useAutoTradingNotifications();
  const [, setForecastData] = useState<ForecastData | null>(null);
  const [chartHtml, setChartHtml] = useState('');
// Subscription status state
const [, setSubscriptionStatus] = useState<string | null>(null);

// Auto-trade state
const [autoTradeEnabled, setAutoTradeEnabled] = useState(false);

// Load auto-trade status on component mount
useEffect(() => {
  const fetchAutoTradeStatus = async () => {
    try {
      const response = await getAutoTradingStatus();
      const data = await response.json();
      setAutoTradeEnabled(data.auto_trading_enabled || false);
    } catch (error) {
      console.error('Error fetching auto-trade status:', error);
      // Fallback to localStorage if API fails
      const savedAutoTradeStatus = localStorage.getItem('autoTradeEnabled');
      if (savedAutoTradeStatus !== null) {
        setAutoTradeEnabled(JSON.parse(savedAutoTradeStatus));
      }
    }
  };

  fetchAutoTradeStatus();
}, []);

// Fetch subscription status from backend
useEffect(() => {
  async function fetchSubscriptionStatus() {
    try {
      // Log authentication state and JWT token
      const token = localStorage.getItem('access_token');
      console.log('[DEBUG] JWT token from localStorage:', token);
      console.log('[DEBUG] Document cookies:', document.cookie);

      const response = await fetch('/api/trading/tier/status', {
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { 'Authorization': `Bearer ${token}` } : {})
        },
        credentials: 'include'
      });
      console.log('[DEBUG] Response status:', response.status, response.statusText);
      if (!response.ok) throw new Error('Failed to fetch subscription status');
      const data = await response.json();
      setSubscriptionStatus(data.status || data.tier || 'Unknown');
    } catch (err) {
      setSubscriptionStatus('Unknown');
      console.error('[DEBUG] Error fetching subscription status:', err);
    }
  }
  fetchSubscriptionStatus();
}, []);
  const [plotlyData, setPlotlyData] = useState<PlotlyData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // Common chart layout configuration
  const getChartLayout = useCallback((customLayout: Partial<PlotParams['layout']> = {}) => ({
    autosize: true,
    height: undefined, // Let the container determine the height
    width: undefined, // Let the container determine the width
    margin: { 
      t: 30, 
      b: 50, // Reduced bottom margin
      l: 50, 
      r: 30, 
      pad: 4,
      autoexpand: true
    },
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    font: {
      color: isDark ? '#f3f4f6' : '#1f2937',
      family: 'Inter, system-ui, -apple-system, sans-serif',
    },
    xaxis: {
      showgrid: true,
      gridcolor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
      linecolor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
      zeroline: false,
    },
    yaxis: {
      showgrid: true,
      gridcolor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
      linecolor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
      zeroline: false,
    },
    showlegend: true,
    legend: {
      orientation: 'h' as const,
      y: -0.2,
      x: 0.5,
      xanchor: 'center' as const,
      bgcolor: 'rgba(0, 0, 0, 0)',
      font: {
        size: 12,
      },
    },
    hovermode: 'x unified' as const,
    hoverlabel: {
      bgcolor: isDark ? '#1f2937' : '#fff',
      font: {
        color: isDark ? '#fff' : '#1f2937',
      },
    },
    ...customLayout,
  }), [isDark]);

  // Update chart theme when theme changes
  useEffect(() => {
    setPlotlyData(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        layout: {
          ...prev.layout,
          ...getChartLayout(prev.layout),
        },
      };
    });
  }, [theme, isDark]); // Only depend on theme and isDark, not plotlyData or getChartLayout

  const fetchForecast = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Update the URL to match the working endpoint
      const url = `/api/trading/forecast?symbol=${symbol}&timeframe=${timeframe}&future_hours=72`;
      console.log('Fetching forecast from:', url);
      
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include' as RequestCredentials
      });
      
      console.log('Response status:', response.status, response.statusText);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to fetch forecast data: ${response.status} ${response.statusText}`);
      }
      
      const data: ForecastData = await response.json();
      console.log('Received forecast data:', data);
      
      if (!data) {
        throw new Error('Received empty response from server');
      }
      
      setForecastData(data);
      
      // If the response contains chart data, try to render it
      if (data.chart_html) {
        console.log('Setting chart HTML, length:', data.chart_html.length);
        setChartHtml(data.chart_html);
        
        // Try to extract Plotly data from the HTML
        try {
          console.log('Attempting to extract Plotly data from HTML...');
          
          // First, try to find the Plotly.newPlot call
          const plotlyCallMatch = data.chart_html.match(/Plotly\.newPlot\([^,]+,\s*(\[.*?\])\s*,\s*({[\s\S]*?})\s*(?:,\s*({[\s\S]*?})\s*)?\)/);
          
          if (plotlyCallMatch) {
            console.log('Found Plotly.newPlot call');
            const dataStr = plotlyCallMatch[1];
            const layoutStr = plotlyCallMatch[2];
            
            try {
              // Clean up the JSON strings (remove comments, fix trailing commas)
              const cleanDataStr = dataStr
                .replace(/\/\*[\s\S]*?\*\//g, '')  // Remove /* */ comments
                .replace(/\/\/.*$/gm, '')             // Remove // comments
                .replace(/,(?=\s*[}\]])/g, '');      // Only remove trailing commas before ] or }
                
              const cleanLayoutStr = layoutStr
                .replace(/\/\*[\s\S]*?\*\//g, '')
                .replace(/\/\/.*$/gm, '')
                .replace(/,(?=\s*[}\]])/g, '');
              
              console.log('Cleaned data string:', cleanDataStr.substring(0, 200) + '...');
              console.log('Cleaned layout string:', cleanLayoutStr.substring(0, 200) + '...');
              
              const plotData = JSON.parse(cleanDataStr);
              const layout = JSON.parse(cleanLayoutStr);
              
              console.log('Successfully parsed Plotly data');
              setPlotlyData({
                data: plotData,
                layout: {
                  ...getChartLayout({
                    ...layout,
                    title: layout.title || `${symbol} Price Forecast`,
                  }),
                  autosize: true,
                  height: 500,
                  width: undefined,
                },
              });
              return;
            } catch (parseError) {
              console.error('Error parsing Plotly data:', parseError);
              console.error('Data string:', dataStr?.substring(0, 200) + '...');
              console.error('Layout string:', layoutStr?.substring(0, 200) + '...');
            }
          } else {
            console.log('No Plotly.newPlot call found in HTML');
          }
          
          // Fallback: Try the previous method
          console.log('Trying fallback extraction method...');
          const parser = new DOMParser();
          const doc = parser.parseFromString(data.chart_html, 'text/html');
          const scriptTags = doc.querySelectorAll('script');
          
          scriptTags.forEach(script => {
            const content = script.textContent || '';
            if (content.includes('Plotly.newPlot')) {
              console.log('Found Plotly script tag');
              try {
                // Extract the data and layout from the script
                const dataMatch = content.match(/data:\s*(\[.*?\])(?:,|\})/s);
                const layoutMatch = content.match(/layout:\s*({[\s\S]*?})(?:,|\})/);
                
                if (dataMatch && layoutMatch) {
                  const dataStr = dataMatch[1];
                  const layoutStr = layoutMatch[1];
                  
                  // Clean up the strings
                  const cleanDataStr = dataStr
                    .replace(/\/\*[\s\S]*?\*\//g, '')
                    .replace(/\/\/.*$/gm, '')
                    .replace(/,(?=\s*[}\]])/g, '');
                    
                  const cleanLayoutStr = layoutStr
                    .replace(/\/\*[\s\S]*?\*\//g, '')
                    .replace(/\/\/.*$/gm, '')
                    .replace(/,(?=\s*[}\]])/g, '');
                  
                  const plotData = JSON.parse(cleanDataStr);
                  const layout = JSON.parse(cleanLayoutStr);
                  
                  setPlotlyData({
                    data: plotData,
                    layout: {
                      ...getChartLayout({
                        ...layout,
                        title: layout.title || `${symbol} Price Forecast`,
                      }),
                      autosize: true,
                      height: 500,
                      width: undefined,
                    },
                  });
                }
              } catch (e) {
                console.error('Error parsing Plotly data from script tag:', e);
              }
            }
          });
        } catch (e) {
          console.error('Error processing chart HTML:', e);
        }
      }
    } catch (err) {
      console.error('Error fetching forecast:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch forecast data');
    } finally {
      setIsLoading(false);
    }
  }, [symbol, timeframe]); // Only include dependencies that are used in the callback

  // Initial data fetch with cleanup
  useEffect(() => {
    let isMounted = true;
    
    const fetchData = async () => {
      try {
        await fetchForecast();
      } catch (error) {
        console.error('Error in initial fetch:', error);
      }
    };

    if (isMounted) {
      fetchData();
    }

    return () => {
      isMounted = false;
    };
  }, [symbol, timeframe]); // Only depend on symbol and timeframe

  // Auto-trade toggle handler
  const handleAutoTradeToggle = useCallback(async (enabled: boolean) => {
    try {
      // Auto-trading toggle works independently of paper mode
      // Paper mode has its own signal generation, live mode has live auto-trading

      // Optimistically update UI
      setAutoTradeEnabled(enabled);

      // Make API call to toggle auto-trading
      const data = await toggleAutoTrading(enabled);

      // Update state with server response
      setAutoTradeEnabled(data.auto_trading_enabled);

      // Also save to localStorage as backup
      localStorage.setItem('autoTradeEnabled', JSON.stringify(data.auto_trading_enabled));

      console.log(`Auto-trade ${data.auto_trading_enabled ? 'enabled' : 'disabled'}`);

      // Show success message
      if (data.auto_trading_enabled && enabled) {
        toastSuccess({
          title: t('notifications.autoTrading.enabledTitle'),
          description: t('notifications.autoTrading.enabledDescription')
        });
      } else if (!data.auto_trading_enabled && !enabled) {
        toast({
          title: t('notifications.autoTrading.disabledTitle'),
          description: t('notifications.autoTrading.disabledDescription')
        });
      }
    } catch (error: any) {
      // Revert the state if API call fails
      setAutoTradeEnabled(!enabled);
      localStorage.setItem('autoTradeEnabled', JSON.stringify(!enabled));

      // Handle specific error cases based on error code
      switch (error.errorCode) {
        case 'insufficient_balance':
          // This is expected validation - log as info, not error
          console.info('Auto-trade toggle blocked: Insufficient balance', {
            current: error.currentBalance,
            required: error.minimumRequired
          });
          toastError({
            title: t('notifications.autoTrading.insufficientBalanceTitle'),
            description: t('notifications.autoTrading.insufficientBalanceDescription', {
              required: error.minimumRequired,
              current: error.currentBalance
            })
          });
          break;
        case 'missing_credentials':
          // This is expected validation - log as info, not error
          console.info('Auto-trade toggle blocked: Missing API credentials');
          toastError({
            title: t('notifications.autoTrading.missingCredentialsTitle'),
            description: t('notifications.autoTrading.missingCredentialsDescription')
          });
          break;
        case 'balance_check_failed':
        case 'balance_verification_failed':
          // This could be a real issue - log as warning
          console.warn('Auto-trade toggle failed: Balance verification error', error.message);
          toastError({
            title: t('notifications.autoTrading.balanceVerificationFailedTitle'),
            description: t('notifications.autoTrading.balanceVerificationFailedDescription')
          });
          break;
        default:
          // Unexpected errors should be logged as errors
          console.error('Unexpected error toggling auto-trade:', error);
          toastError({
            title: t('notifications.autoTrading.errorTitle'),
            description: error.message || t('notifications.autoTrading.errorDescription')
          });
      }
    }
  }, []);

  // Note: Removed mobile redirect to ensure feature parity between desktop and mobile

  return (
    <div className={`${isMobile ? 'p-3 space-y-4 w-full min-w-0 max-w-full box-border' : 'p-6 space-y-6'} max-w-full overflow-x-hidden`} style={isMobile ? { maxWidth: '100vw' } : {}}>
      {/* Paper Trading Mode Banner */}
      {isPaperMode && (
        <div className={`bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg ${isMobile ? 'p-3' : 'p-4'} mb-4 w-full min-w-0 max-w-full overflow-hidden`}>
          <div className={`flex ${isMobile ? 'flex-col gap-2' : 'items-center justify-between'}`}>
            <div className="flex items-center space-x-3 min-w-0 flex-1">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
              </div>
              <div className="flex-1 min-w-0">
                <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-orange-800 dark:text-orange-200`}>
                  {t('trading.paperTradingActive')}
                </h3>
                <p className={`${isMobile ? 'text-xs' : 'text-xs'} text-orange-600 dark:text-orange-300 mt-1 truncate`}>
                  {isMobile
                    ? `Virtual: $${paperBalance?.toLocaleString() || '0'}`
                    : `${t('trading.paperTradingDesc')} • Virtual Balance: $${paperBalance?.toLocaleString() || '0'}`
                  }
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPaperTradingHelp(true)}
              className={`text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 flex-shrink-0 ${isMobile ? 'self-start' : ''}`}
            >
              <BookOpen className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'} ${isMobile ? '' : 'mr-1'}`} />
              {!isMobile && <span className="text-xs">{t('trading.learnMore')}</span>}
            </Button>
          </div>
        </div>
      )}

      <div className={`flex ${isMobile ? 'flex-col gap-3' : 'items-center justify-between'} mb-4 sm:mb-6`}>
        <h1 className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold`}>{t('dashboard.title')}</h1>
        <div className={`${isMobile ? 'self-start' : ''}`}>
          <LiveBTCPrice />
        </div>
      </div>

      {/* Deactivated Account Alert */}
      <DeactivatedAccountAlert />
      {/* Top grid: Balance, PnL, Monthly Rate, Win Rate, Tier */}
      <DashboardTopGrid
        key={`${isPaperMode}-${paperBalance}`}
        isPaperModeProp={isPaperMode}
        paperBalanceProp={paperBalance}
      />
      {/* Risk Settings */}
      <RiskSettingsPanel isPaperMode={isPaperMode} />
      {/* Forecast Chart */}
      <div className={`${isMobile ? 'mb-4' : 'mb-6'}`}>
        <div className={`${isMobile ? 'space-y-3 mt-4' : 'space-y-4 mt-6'}`}>
          <div className={`${isMobile ? 'flex flex-col gap-3' : 'flex justify-between items-center'}`}>
            <div className={`${isMobile ? 'flex flex-col gap-2' : 'flex items-center space-x-4'}`}>
              <h2 className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold`}>{t('dashboard.forecast.title')}</h2>

              {/* Paper Trading Mode Toggle */}
              <div className={`flex items-center ${isMobile ? 'gap-2' : 'space-x-2'}`}>
                <div className="flex items-center space-x-1">
                  <span className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600 dark:text-gray-400`}>
                    {t('trading.tradingMode')}
                  </span>
                  <PaperTradingTooltip>
                    {t('trading.paperTradingDesc')}. {t('paperTradingHelp.buildConfidenceDesc')}!
                  </PaperTradingTooltip>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={isPaperMode}
                    onChange={(e) => switchTradingMode(e.target.checked)}
                    disabled={paperTradingLoading}
                  />
                  <div className={`${isMobile ? 'w-8 h-4' : 'w-9 h-5'} bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer peer-checked:after:translate-x-4 peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full ${isMobile ? 'after:h-3 after:w-3' : 'after:h-4 after:w-4'} after:transition-all dark:border-gray-600 peer-checked:bg-orange-500`}></div>
                  <span className={`ml-2 ${isMobile ? 'text-xs' : 'text-xs'} font-medium ${isPaperMode ? 'text-orange-600 dark:text-orange-400' : 'text-gray-700 dark:text-gray-300'}`}>
                    {isPaperMode ? t('trading.paperMode') : t('trading.liveMode')}
                  </span>
                </label>
                {isPaperMode && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPaperTradingHelp(true)}
                    className="text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 p-1"
                  >
                    <BookOpen className="w-4 h-4" />
                  </Button>
                )}
              </div>

              {/* Auto-Trade Toggle */}
              <div className={`flex items-center ${isMobile ? 'gap-2' : 'space-x-2'}`}>
                <span className={`${isMobile ? 'text-xs' : 'text-sm'} ${isPaperMode ? 'text-gray-400 dark:text-gray-500' : 'text-gray-600 dark:text-gray-400'}`}>
                  {t('trading.autoTrading')}
                </span>
                <label className={`relative inline-flex items-center ${isPaperMode ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}>
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={autoTradeEnabled && !isPaperMode}
                    onChange={(e) => handleAutoTradeToggle(e.target.checked)}
                    disabled={isPaperMode}
                  />
                  <div className={`${isMobile ? 'w-8 h-4' : 'w-9 h-5'} ${isPaperMode ? 'bg-gray-300 dark:bg-gray-600' : 'bg-gray-200 dark:bg-gray-700'} peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-4 peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full ${isMobile ? 'after:h-3 after:w-3' : 'after:h-4 after:w-4'} after:transition-all dark:border-gray-600 ${isPaperMode ? 'peer-checked:bg-gray-400' : 'peer-checked:bg-blue-600'}`}></div>
                  <span className={`ml-2 ${isMobile ? 'text-xs' : 'text-xs'} font-medium ${isPaperMode ? 'text-gray-400 dark:text-gray-500' : 'text-gray-700 dark:text-gray-300'}`}>
                    {(autoTradeEnabled && !isPaperMode) ? t('common.active') : t('common.inactive')}
                    {isPaperMode && <span className="text-orange-500 ml-1">({t('trading.paperModeTitle')})</span>}
                  </span>
                </label>
              </div>
            </div>
            <div className={`flex items-center ${isMobile ? 'gap-2 self-start' : 'space-x-2'}`}>
              <Button
                variant="outline"
                size={isMobile ? "sm" : "sm"}
                onClick={fetchForecast}
                disabled={isLoading}
                className={isMobile ? 'text-xs px-3 py-1.5' : ''}
              >
                <RefreshCw className={`${isMobile ? 'w-3 h-3 mr-1' : 'w-4 h-4 mr-2'} ${isLoading ? 'animate-spin' : ''}`} />
                {t('dashboard.forecast.refresh')}
              </Button>
            </div>
          </div>
          <div className={`overflow-hidden ${isMobile ? 'w-full' : ''}`}>
            <div className="p-0">
              <div className={`${isMobile ? 'h-[300px] w-full min-w-0' : 'h-[500px] w-full'} relative`}>
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                  </div>
                ) : error ? (
                  <div className="flex flex-col items-center justify-center h-full text-destructive p-4 text-center">
                    <p className="font-medium">{t('dashboard.forecast.errorLoading')}</p>
                    <p className="text-sm text-muted-foreground">{error}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={fetchForecast}
                    >
                      {t('dashboard.forecast.retry')}
                    </Button>
                  </div>
                ) : plotlyData ? (
                  <Suspense fallback={
                    <div className="flex items-center justify-center h-full">
                      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                    </div>
                  }>
                    <Plot
                      data={plotlyData.data}
                      layout={{
                        ...plotlyData.layout,
                        autosize: true,
                        height: isMobile ? 300 : 500,
                        width: undefined,
                        margin: isMobile ? { l: 30, r: 10, t: 30, b: 30 } : plotlyData.layout.margin,
                        ...(isMobile && {
                          xaxis: {
                            ...plotlyData.layout.xaxis,
                            fixedrange: true,
                          },
                          yaxis: {
                            ...plotlyData.layout.yaxis,
                            fixedrange: true,
                          }
                        })
                      }}
                      useResizeHandler={true}
                      style={{ width: '100%', height: '100%', minWidth: '0' }}
                      config={{
                        displayModeBar: !isMobile,
                        responsive: true,
                        scrollZoom: !isMobile,
                        displaylogo: false,
                        ...(isMobile && { staticPlot: false }),
                        modeBarButtonsToRemove: [
                          'select2d', 'lasso2d', 'autoScale2d',
                          'toggleSpikelines', 'hoverClosestCartesian',
                          'hoverCompareCartesian', 'zoomIn2d', 'zoomOut2d',
                          'zoom2d', 'pan2d', 'select2d', 'toImage', 'resetScale2d'
                        ],
                      }}
                    />
                  </Suspense>
                ) : chartHtml ? (
                  <div
                    ref={chartContainerRef}
                    className="w-full h-full"
                    dangerouslySetInnerHTML={{ __html: chartHtml }}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    {t('dashboard.forecast.noData')}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        {/* --- End original forecast chart code --- */}
      </div>

      {/* Paper Trading Dashboard - Only shown in paper mode */}
      {isPaperMode && <PaperTradingDashboard className={isMobile ? 'w-full min-w-0 max-w-full overflow-hidden' : ''} />}

      {/* Active open position - mode-aware */}
      <ActivePosition key={`active-positions-${modeChangeKey}`} />
      {/* Profit Share Summary - Only shown in live mode */}
      {!isPaperMode && <ProfitShareSummary />}
      {/* Paginated trading history - mode-aware */}
      <TradingHistory key={`trading-history-${modeChangeKey}`} />
      {/* UI feedback for blocked users */}
      <BlockedUserAlert />

      {/* Paper Trading Help Modal */}
      <PaperTradingHelp
        isOpen={showPaperTradingHelp}
        onClose={() => setShowPaperTradingHelp(false)}
      />
    </div>
  );
}



// Risk Settings Panel Component
interface RiskSettingsPanelProps {
  isPaperMode?: boolean;
}

export const RiskSettingsPanel = function RiskSettingsPanel({ isPaperMode: propIsPaperMode }: RiskSettingsPanelProps) {
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const { isPaperMode: hookIsPaperMode } = usePaperTrading();

  // Use prop if provided, otherwise fall back to hook
  const isPaperMode = propIsPaperMode !== undefined ? propIsPaperMode : hookIsPaperMode;

  // Debug log for paper mode state
  console.log(`[RiskSettingsPanel] Component render - isPaperMode: ${isPaperMode}, propIsPaperMode: ${propIsPaperMode}, hookIsPaperMode: ${hookIsPaperMode}`);

  // Debug log to verify paper mode detection
  console.log('[RiskSettingsPanel] isPaperMode:', isPaperMode);

  const [_riskSettings, setRiskSettings] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [investmentPercentage, setInvestmentPercentage] = useState(0);
  const [leverage, setLeverage] = useState(1);
  const [maxLeverage, setMaxLeverage] = useState(1);
  const [currentTier, setCurrentTier] = useState(1);
  const [exchange, setExchange] = useState('binance');
  const [accountType, setAccountType] = useState('SPOT');

  useEffect(() => {
    fetchRiskSettings();
  }, []);

  const fetchRiskSettings = useCallback(async () => {
    try {
      setLoading(true);
      const data = await getRiskSettings();
      setRiskSettings(data);
      setInvestmentPercentage(data.investment_percentage || 0);
      setLeverage(data.leverage || 1);
      setMaxLeverage(data.max_leverage_for_exchange || 1);
      setCurrentTier(data.current_tier || 1);
      setExchange(data.exchange || 'binance');
      setAccountType(data.account_type || 'SPOT');
    } catch (error) {
      console.error('Error fetching risk settings:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const handleSaveSettings = useCallback(async () => {
    try {
      setSaving(true);
      const response = await saveRiskSettings({
        investment_percentage: investmentPercentage,
        leverage: leverage,
        exchange: exchange,
        account_type: accountType
      });

      // Update only the risk settings state without full refresh
      if (response.settings) {
        setRiskSettings((prev: any) => ({
          ...prev,
          ...response.settings,
          is_configured: true
        }));
      }

      // Show success message
      console.log('Risk settings saved successfully');
      // You can replace this with a proper toast notification system
    } catch (error: any) {
      console.error('Error saving risk settings:', error);
      // Show error message - you can replace this with a proper toast notification
      const errorMessage = error.response?.data?.error || 'Failed to save risk settings';
      alert(errorMessage);
    } finally {
      setSaving(false);
    }
  }, [investmentPercentage, leverage, exchange, accountType]);

  const handleResetToSafe = useCallback(() => {
    setInvestmentPercentage(0);
    setLeverage(1);
  }, []);

  const getExchangeDisplayName = useCallback((exchangeKey: string) => {
    const names = {
      'binance': 'Binance',
      'binance_us': 'Binance US',
      'kraken': 'Kraken',
      'bitso': 'Bitso'
    };
    return names[exchangeKey as keyof typeof names] || exchangeKey;
  }, []);

  const getExchangeMarketInfo = useCallback((exchangeKey: string, _accountType: string) => {
    const info = {
      'binance': { market: 'Futures', maxLeverage: '125x' },
      'binance_us': { market: 'Spot Margin', maxLeverage: '3x' },
      'kraken': { market: 'Futures', maxLeverage: '100x' },
      'bitso': { market: 'Spot', maxLeverage: '1x' }
    };
    return info[exchangeKey as keyof typeof info] || { market: 'Spot', maxLeverage: '1x' };
  }, []);

  if (loading) {
    return (
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="text-center">{t('dashboard.riskSettings.loading')}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${isMobile ? 'mb-4 w-full min-w-0' : 'mb-6'} max-w-full overflow-hidden`}>
      <CardContent className={`${isMobile ? 'p-3' : 'p-6'}`}>
        <div className="flex items-center justify-between mb-3">
          <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold`}>
            {isPaperMode ? t('dashboard.riskSettings.paperTradingTitle') : t('dashboard.riskSettings.title')}
          </h3>
          {isPaperMode && (
            <span className="text-xs font-medium text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30 px-2 py-1 rounded">
              {t('trading.paperTradingLabel')}
            </span>
          )}
        </div>

        <div className={`grid grid-cols-1 ${isMobile ? 'gap-4' : 'md:grid-cols-2 gap-6'}`}>
          {/* Investment Percentage Slider */}
          <div>
            <Slider
              value={investmentPercentage}
              onChange={setInvestmentPercentage}
              min={0}
              max={10}
              step={0.1}
              label={t('dashboard.riskSettings.investmentPercentage')}
              unit="%"
              disabled={saving}
            />
            <p className="text-xs text-gray-500 mt-1">
              {t('dashboard.riskSettings.investmentDescription')}
            </p>
          </div>

          {/* Leverage Slider */}
          <div>
            <Slider
              value={leverage}
              onChange={setLeverage}
              min={1}
              max={isPaperMode ? 100 : maxLeverage}
              step={0.1}
              label={t('dashboard.riskSettings.leverage')}
              unit="x"
              disabled={saving}
            />
            <p className="text-xs text-gray-500 mt-1">
              {isPaperMode
                ? t('dashboard.riskSettings.paperTradingLeverageDesc')
                : t('dashboard.riskSettings.leverageDescription', {
                    max: maxLeverage,
                    tier: currentTier,
                    exchange: getExchangeDisplayName(exchange),
                    market: getExchangeMarketInfo(exchange, accountType).market
                  })
              }
            </p>
          </div>
        </div>

        {/* Exchange and Account Type Info */}
        <div className={`mt-4 ${isMobile ? 'p-2' : 'p-3'} bg-blue-50 dark:bg-blue-900/20 rounded-lg w-full min-w-0 max-w-full overflow-hidden`}>
          <div className={`flex ${isMobile ? 'flex-col gap-2' : 'items-center justify-between'}`}>
            <div className="min-w-0 flex-1">
              <div className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium mb-1`}>{t('dashboard.riskSettings.exchange')}</div>
              <div className={`${isMobile ? 'text-xs' : 'text-sm'} truncate`}>
                {getExchangeDisplayName(exchange)} {getExchangeMarketInfo(exchange, accountType).market}
                <span className="text-xs text-gray-500 ml-2">
                  ({t('trading.max')}: {getExchangeMarketInfo(exchange, accountType).maxLeverage})
                </span>
              </div>
            </div>
            <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-blue-600 dark:text-blue-400 flex-shrink-0 ${isMobile ? 'self-start' : ''}`}>
              {isMobile ? t('dashboard.riskSettings.configureCredentials').substring(0, 20) + '...' : t('dashboard.riskSettings.configureCredentials')}
            </div>
          </div>
        </div>

        {/* Paper Trading Mode Note */}
        {isPaperMode && (
          <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
            <div className="flex items-center gap-2">
              <span className="text-orange-600 dark:text-orange-400">📊</span>
              <span className="text-sm text-orange-700 dark:text-orange-300">
                {t('dashboard.riskSettings.paperTradingNote')}
              </span>
            </div>
          </div>
        )}

        {/* Warning for 0% investment */}
        {investmentPercentage === 0 && (
          <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
            <div className="flex items-center gap-2">
              <span className="text-orange-600 dark:text-orange-400">⚠️</span>
              <span className="text-sm text-orange-700 dark:text-orange-300">
                {t('dashboard.riskSettings.warningZeroInvestment')}
              </span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 mt-6">
          <Button
            onClick={handleSaveSettings}
            disabled={saving || loading}
            className="flex-1"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                {t('dashboard.riskSettings.saving')}
              </>
            ) : (
              t('dashboard.riskSettings.saveSettings')
            )}
          </Button>
          <Button
            variant="outline"
            onClick={handleResetToSafe}
            disabled={saving || loading}
          >
            {t('dashboard.riskSettings.resetToSafe')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// --- DashboardTopGrid, TierSubscriptionSidebar, ActivePosition, TradingHistory, BlockedUserAlert components ---


// Top grid: Balance, PnL, Monthly Rate, Win Rate, Tier
export function DashboardTopGrid({
  isPaperModeProp,
  paperBalanceProp
}: {
  isPaperModeProp?: boolean;
  paperBalanceProp?: number;
} = {}) {
  const [stats, setStats] = useState<any>(null);
  const [balance, setBalance] = useState<string | null>(null);
  const [, setRealTimeData] = useState<any>(null);
  const { t } = useTranslation();
  const { isMobile } = useMobile();

  // Use global NFT verification context
  const { isVerifying, tierStatus, refreshTierStatus } = useNFTVerificationContext();

  // Paper trading hook (fallback if props not provided)
  const { isPaperMode: hookIsPaperMode, balance: hookPaperBalance } = usePaperTrading();

  // Use props if provided, otherwise use hook values
  const isPaperMode = isPaperModeProp !== undefined ? isPaperModeProp : hookIsPaperMode;
  const paperBalance = paperBalanceProp !== undefined ? paperBalanceProp : hookPaperBalance;



  // Function to refresh dashboard data
  const refreshDashboardData = useCallback(async () => {
    try {
      if (isPaperMode) {
        // Fetch paper trading analytics
        const paperAnalytics = await paperTradingApi.getPaperTradingAnalytics(30);
        if (paperAnalytics.success && paperAnalytics.summary) {
          // Convert paper trading analytics to stats format
          const paperStats = {
            total_pnl: paperAnalytics.summary.total_pnl || 0,
            pnl: paperAnalytics.summary.total_pnl || 0,
            monthly_rate: paperAnalytics.summary.total_pnl || 0, // Use total PnL as monthly rate for paper trading
            monthlyRate: paperAnalytics.summary.total_pnl || 0,
            win_rate: paperAnalytics.summary.win_rate || 0,
            winRate: paperAnalytics.summary.win_rate || 0
          };
          setStats(paperStats);
        }
      } else {
        // Fetch live trading statistics
        const liveStats = await getTradingStatistics();
        setStats(liveStats);

        // Fetch real balance for live mode
        const balanceData = await getRealBalance();
        const newBalance = balanceData?.balance ? `$${Number(balanceData.balance).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 8})}` : "--";
        setBalance(newBalance);
      }

      refreshTierStatus();
    } catch (error) {
      console.error('Error refreshing dashboard data:', error);
    }
  }, [isPaperMode, refreshTierStatus]);

  useEffect(() => {
    // Initial data fetch
    refreshDashboardData();

    // Subscribe to real-time updates with proper authentication
    const token = localStorage.getItem('access_token');
    if (!token) {
      console.error('No auth token found for SSE connection');
      return;
    }

    // Check if token is expired (basic check - decode JWT)
    try {
      const tokenPayload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      if (tokenPayload.exp && tokenPayload.exp < currentTime) {
        console.warn('JWT token is expired, skipping SSE connection');
        return;
      }
    } catch (e) {
      console.error('Invalid JWT token format');
      return;
    }

    // Note: EventSource doesn't support custom headers, so we pass token as query parameter
    const eventSource = new EventSource(`/api/realtime/stream?token=${encodeURIComponent(token)}`, {
      withCredentials: true
    });

    eventSource.onopen = () => {
      console.log('SSE connection established');
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('Received SSE data:', data);

        setRealTimeData(data);

        if (data.balance !== undefined && data.balance !== null) {
          setBalance(`$${Number(data.balance).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 8})}`);
        }

        if (data.tier_status) {
          // Refresh global tier status when SSE updates come in
          refreshTierStatus();
        }

        if (data.error) {
          console.error('SSE data error:', data.error);
        }
      } catch (error) {
        console.error('Error parsing real-time data:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
      // Don't immediately reconnect to avoid spam
    };

    return () => {
      console.log('Closing SSE connection');
      eventSource.close();
    };
  }, [refreshTierStatus]);

  // Watch for paper trading mode changes and refresh data
  useEffect(() => {
    // Add a small delay to ensure the mode switch is complete
    const timeoutId = setTimeout(() => {
      refreshDashboardData();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [isPaperMode, paperBalance, refreshDashboardData]);



  return (
    <div className={`grid ${isMobile ? 'grid-cols-1 gap-3 mb-4 w-full min-w-0' : 'gap-4 md:grid-cols-2 lg:grid-cols-5 mb-6'} max-w-full`}>
      <Card>
        <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
          <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-muted-foreground`}>
            {isPaperMode ? t('trading.virtualBalance') : t('dashboard.balance')}
          </h3>
          <p className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold ${isPaperMode ? 'text-orange-600 dark:text-orange-400' : ''}`}>
            {isPaperMode
              ? `$${(paperBalance || 0).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`
              : (balance ?? "--")
            }
          </p>
          {isPaperMode && (
            <p className="text-xs text-orange-500 dark:text-orange-400 mt-1">{t('trading.paperTradingLabel')}</p>
          )}
        </CardContent>
      </Card>
      <Card>
        <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
          <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-muted-foreground`}>
            {t('dashboard.pnlToday')}
          </h3>
          <p className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold ${isPaperMode ? 'text-orange-600 dark:text-orange-400' : ''}`}>
            {stats ? `${stats.total_pnl ?? stats.pnl ?? "--"}%` : "--"}
          </p>
          {isPaperMode && (
            <p className="text-xs text-orange-500 dark:text-orange-400 mt-1">{t('trading.paperTradingLabel')}</p>
          )}
        </CardContent>
      </Card>
      <Card>
        <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
          <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-muted-foreground`}>
            {t('dashboard.monthlyRate')}
          </h3>
          <p className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold ${isPaperMode ? 'text-orange-600 dark:text-orange-400' : ''}`}>
            {stats ? `${stats.monthly_rate ?? stats.monthlyRate ?? "--"}%` : "--"}
          </p>
          {isPaperMode && (
            <p className="text-xs text-orange-500 dark:text-orange-400 mt-1">{t('trading.paperTradingLabel')}</p>
          )}
        </CardContent>
      </Card>
      <Card>
        <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
          <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-muted-foreground`}>
            {t('dashboard.winRate')}
          </h3>
          <p className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold ${isPaperMode ? 'text-orange-600 dark:text-orange-400' : ''}`}>
            {stats ? `${stats.win_rate ?? stats.winRate ?? "--"}%` : "--"}
          </p>
          {isPaperMode && (
            <p className="text-xs text-orange-500 dark:text-orange-400 mt-1">{t('trading.paperTradingLabel')}</p>
          )}
        </CardContent>
      </Card>
      <Card>
        <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
          <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-muted-foreground`}>
            {t('dashboard.tier')}
          </h3>
          <div className="flex items-center gap-2">
            <p className={`${isMobile ? 'text-base' : 'text-lg'} font-bold ${isPaperMode ? 'text-orange-600 dark:text-orange-400' : ''}`}>
              {isPaperMode
                ? t('trading.paperModeTitle')
                : tierStatus
                  ? tierStatus.tier_1
                    ? t('tiers.tier1.name')
                    : tierStatus.tier_2
                    ? t('tiers.tier2.name')
                    : tierStatus.tier_3
                    ? t('tiers.tier3.name')
                    : "--"
                  : "--"
              }
            </p>
            {!isPaperMode && isVerifying && (
              <div className={`animate-spin rounded-full ${isMobile ? 'h-3 w-3' : 'h-4 w-4'} border-t-2 border-b-2 border-primary`}></div>
            )}
          </div>

          {/* Paper Mode Information */}
          {isPaperMode ? (
            <>
              <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-orange-500 dark:text-orange-400`}>
                {t('trading.noProfitSharePaper')}
              </p>
              <p className="text-xs text-orange-500 dark:text-orange-400 mt-1">{t('trading.paperTradingActive')}</p>
            </>
          ) : (
            /* Live Mode Information */
            <>
              <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
                {tierStatus?.tier_1 && t('dashboard.profitShare30')}
                {tierStatus?.tier_2 && t('dashboard.profitShare20')}
                {tierStatus?.tier_3 && t('dashboard.profitShare10')}
                {!tierStatus?.tier_1 && !tierStatus?.tier_2 && !tierStatus?.tier_3 && "--"}
              </p>
              {isVerifying && tierStatus?.tier_3 && (
                <p className="text-xs text-blue-600 mt-1">
                  {t('dashboard.verifyingNFT')}
                </p>
              )}
              {tierStatus?.profit_share_owed !== undefined && tierStatus.profit_share_owed > 0 && (
                <p className="text-xs text-red-600 mt-1">
                  {t('dashboard.debt')}: ${Number(tierStatus.profit_share_owed).toFixed(2)}
                </p>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}


// Active open trading positions - displaying both app and external (mode-aware)
export function ActivePosition() {
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const [positions, setPositions] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'app' | 'external'>('all');

  // Paper trading hook for mode awareness
  const { isPaperMode } = usePaperTrading();

  // Debug logging for paper mode state
  console.log(`[ActivePositions] Component render - isPaperMode: ${isPaperMode}, positions:`, positions);

  const fetchPositions = async (source: 'all' | 'app' | 'external' = 'all') => {
    console.log(`[ActivePositions] fetchPositions called - isPaperMode: ${isPaperMode}, source: ${source}`);
    setLoading(true);
    setError(null);

    // Clear existing positions immediately when switching modes
    setPositions(null);

    try {
      let response;
      let data;

      if (isPaperMode) {
        console.log('[ActivePositions] Fetching paper trading positions...');
        // Fetch paper trading positions
        const paperPositions = await paperTradingApi.getPaperPositions();
        if (paperPositions.success) {
          data = {
            positions: paperPositions.positions || [],
            source: 'paper',
            isPaperMode: true
          };
          console.log('[ActivePositions] Paper positions data received:', data);
          setPositions(data);
        } else {
          throw new Error('Failed to fetch paper positions');
        }
      } else {
        console.log(`[ActivePositions] Fetching live trading positions for source: ${source}...`);
        // Fetch live trading positions
        response = await fetch(`/api/trading/active_position?source=${source}`, {
          credentials: 'include',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          },
        });

        if (response.ok) {
          data = await response.json();
          console.log('[ActivePositions] Live positions data received:', data);
          setPositions(data);

          // Log diagnostics if available (live mode only)
          if (data.diagnostics) {
            console.log('ActivePosition diagnostics:', data.diagnostics);

            // Show helpful messages based on diagnostics
            if (data.diagnostics.credential_status === "No active API credentials found") {
              console.warn("No API credentials found - external positions will be empty");
            }
            if (data.diagnostics.external_error) {
              console.error("External positions error:", data.diagnostics.external_error);
            }
          }
        } else {
          const errorData = await response.json().catch(() => ({}));
          setError(errorData.error || t('dashboard.activePositions.errorLoading'));
          console.error('ActivePosition fetch failed:', response.status, errorData);
        }
      }
    } catch (e: any) {
      if (e?.response?.status === 401) {
        setError(t('errors.unauthorized'));
      } else {
        setError(e?.message || t('dashboard.activePositions.errorLoading'));
      }
      setPositions(null);
      console.error('ActivePosition fetch error:', e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log(`[ActivePositions] useEffect triggered - isPaperMode: ${isPaperMode}, activeTab: ${activeTab}`);
    fetchPositions(activeTab);
  }, [activeTab, isPaperMode]); // Auto-refresh when trading mode changes

  // Additional effect to handle mode changes specifically
  useEffect(() => {
    console.log(`[ActivePositions] Mode change detected - isPaperMode: ${isPaperMode}`);
    // Reset tab to 'all' when switching to live mode for full data fetch
    if (!isPaperMode && activeTab !== 'all') {
      setActiveTab('all');
    }
  }, [isPaperMode]);

  const renderPositions = (positionList: any[], _title: string, source: string) => {
    if (!positionList || positionList.length === 0) {
      return (
        <div className="text-muted-foreground text-center py-8">
          <p className="text-lg font-medium mb-2">{t('dashboard.activePositions.noPositions')}</p>
          <p className="text-sm">
            {source === 'app' ? t('dashboard.activePositions.noAppPositions') :
             source === 'external' ? t('dashboard.activePositions.noExternalPositions') :
             t('dashboard.activePositions.noPositionsDesc')}
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {/* Section Header */}
        <div className="flex items-center justify-between">
          <h4 className={`font-medium text-sm flex items-center gap-2 ${source === 'paper' ? 'text-orange-600 dark:text-orange-400' : 'text-muted-foreground'}`}>
            {source === 'paper' ? t('trading.paperTradingPositions') :
             source === 'app' ? t('dashboard.activePositions.positionTypes.appPositions') :
             t('dashboard.activePositions.positionTypes.externalPositions')}
            <span className={`text-xs px-2 py-1 rounded ${source === 'paper' ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400' : 'bg-muted'}`}>
              {positionList.length} {positionList.length === 1 ? 'position' : 'positions'}
            </span>
            {source === 'paper' && (
              <span className="text-xs text-orange-500 dark:text-orange-400">{t('trading.paperTradingActive')}</span>
            )}
          </h4>
          {/* CEX Indicator */}
          <div className={`text-xs ${source === 'paper' ? 'text-orange-500 dark:text-orange-400' : 'text-muted-foreground'}`}>
            {source === 'paper' ? t('trading.paperTradingMode') : `CEX: ${source === 'app' ? t('trading.internal') : t('trading.external')}`}
          </div>
        </div>

        {/* Modern Table Design - Desktop */}
        <div className="bg-card border border-border rounded-lg overflow-hidden shadow-sm">
          {/* Desktop Table Header */}
          <div className="hidden md:block bg-muted/50 border-b border-border">
            <div className="grid grid-cols-12 gap-2 px-4 py-3 text-xs font-medium text-muted-foreground uppercase tracking-wide">
              <div className="col-span-2">Symbol</div>
              <div className="col-span-1 text-center">Size</div>
              <div className="col-span-1 text-center">Entry Price</div>
              <div className="col-span-1 text-center">Mark Price</div>
              <div className="col-span-1 text-center">Liq. Price</div>
              <div className="col-span-1 text-center">Margin</div>
              <div className="col-span-1 text-center">PNL (ROI %)</div>
              <div className="col-span-1 text-center">TP/SL</div>
              <div className="col-span-3 text-center">Actions</div>
            </div>
          </div>

          {/* Mobile Table Header */}
          <div className="md:hidden bg-muted/50 border-b border-border px-4 py-3">
            <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Active Positions
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-border">
            {positionList.map((position, idx) => {
              console.log('Position object:', position);
              const isProfit = position.unrealized_pnl >= 0;
              const breakEvenPrice = position.entry_price; // Simplified - could be calculated based on fees

              return (
                <div key={idx}>
                  {/* Desktop Row */}
                  <div className="hidden md:grid grid-cols-12 gap-2 px-4 py-3 text-sm hover:bg-muted/30 transition-colors">
                    {/* Symbol Column */}
                    <div className="col-span-2 flex items-center gap-2">
                      <div className="flex flex-col">
                        <div className="font-semibold">{position.symbol}</div>
                        <div className="flex items-center gap-1">
                          <span className={`px-1.5 py-0.5 text-xs rounded font-medium ${
                            position.side?.toLowerCase() === 'long'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                              : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                          }`}>
                            {position.side?.toUpperCase()}
                          </span>
                          <span className={`px-1.5 py-0.5 text-xs rounded ${
                            source === 'app'
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                              : 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
                          }`}>
                            {source.toUpperCase()}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Size Column */}
                    <div className="col-span-1 text-center">
                      <div className="font-medium">
                        {position.size || position.quantity || '--'}
                      </div>
                    </div>

                    {/* Entry Price Column */}
                    <div className="col-span-1 text-center">
                      <div className="font-medium">
                        ${position.entry_price ? Number(position.entry_price).toFixed(2) : '--'}
                      </div>
                    </div>

                    {/* Mark Price Column */}
                    <div className="col-span-1 text-center">
                      <div className="font-medium">
                        ${position.current_price ? Number(position.current_price).toFixed(2) : '--'}
                      </div>
                    </div>

                    {/* Liquidation Price Column */}
                    <div className="col-span-1 text-center">
                      <div className="font-medium text-orange-600">
                        {position.liquidation_price !== null && position.liquidation_price !== undefined
                          ? `$${Number(position.liquidation_price).toFixed(2)}`
                          : '--'}
                      </div>
                    </div>

                    {/* Margin Column */}
                    <div className="col-span-1 text-center">
                      <div className="font-medium">
                        {position.margin_used ? `${Number(position.margin_used).toFixed(2)}` : '--'}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {position.margin_rate ? `${Number(position.margin_rate).toFixed(1)}%` : ''}
                      </div>
                    </div>

                    {/* PNL Column */}
                    <div className="col-span-1 text-center">
                      <div className={`font-bold ${isProfit ? 'text-green-600' : 'text-red-600'}`}>
                        {position.unrealized_pnl
                          ? `${isProfit ? '+' : ''}${Number(position.unrealized_pnl).toFixed(2)}`
                          : '--'}
                      </div>
                      <div className={`text-xs ${isProfit ? 'text-green-600' : 'text-red-600'}`}>
                        {position.unrealized_roi
                          ? `${isProfit ? '+' : ''}${Number(position.unrealized_roi).toFixed(2)}%`
                          : '--'}
                      </div>
                    </div>

                    {/* TP/SL Column */}
                    <div className="col-span-1 text-center">
                      <div className="text-xs space-y-1">
                        <div className="text-green-600">
                          TP: {position.take_profit ? `$${Number(position.take_profit).toFixed(2)}` : '--'}
                        </div>
                        <div className="text-red-600">
                          SL: {position.stop_loss ? `$${Number(position.stop_loss).toFixed(2)}` : '--'}
                        </div>
                      </div>
                    </div>

                    {/* Actions Column */}
                    <div className="col-span-3 flex items-center justify-center gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-7 px-2 text-xs"
                        onClick={() => console.log('Market close position:', position.id)}
                      >
                        {t('trading.marketClose')}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-7 px-2 text-xs"
                        onClick={() => console.log('Reverse position:', position.id)}
                      >
                        {t('trading.reverse')}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-7 px-2 text-xs"
                        onClick={() => console.log('Edit TP/SL for position:', position.id)}
                      >
                        {t('trading.editTpSl')}
                      </Button>
                    </div>
                  </div>

                  {/* Mobile Card */}
                  <div className="md:hidden p-4 hover:bg-muted/30 transition-colors">
                    <div className="space-y-3">
                      {/* Mobile Header */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-base">{position.symbol}</span>
                          <span className={`px-2 py-1 text-xs rounded font-medium ${
                            position.side?.toLowerCase() === 'long'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                              : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                          }`}>
                            {position.side?.toUpperCase()}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded ${
                            source === 'app'
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                              : 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
                          }`}>
                            {source.toUpperCase()}
                          </span>
                        </div>
                        <div className={`text-right font-bold ${isProfit ? 'text-green-600' : 'text-red-600'}`}>
                          <div>
                            {position.unrealized_pnl
                              ? `${isProfit ? '+' : ''}${Number(position.unrealized_pnl).toFixed(2)} USDT`
                              : '--'}
                          </div>
                          <div className="text-xs">
                            {position.unrealized_roi
                              ? `${isProfit ? '+' : ''}${Number(position.unrealized_roi).toFixed(2)}%`
                              : '--'}
                          </div>
                        </div>
                      </div>

                      {/* Mobile Details Grid */}
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div>
                          <div className="text-muted-foreground text-xs">Size</div>
                          <div className="font-medium">{position.size || position.quantity || '--'}</div>
                        </div>
                        <div>
                          <div className="text-muted-foreground text-xs">Entry Price</div>
                          <div className="font-medium">
                            ${position.entry_price ? Number(position.entry_price).toFixed(2) : '--'}
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground text-xs">Mark Price</div>
                          <div className="font-medium">
                            ${position.current_price ? Number(position.current_price).toFixed(2) : '--'}
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground text-xs">Margin</div>
                          <div className="font-medium">
                            {position.margin_used ? `${Number(position.margin_used).toFixed(2)}` : '--'}
                          </div>
                        </div>
                      </div>

                      {/* Mobile TP/SL */}
                      <div className="flex justify-between text-xs">
                        <div className="text-green-600">
                          TP: {position.take_profit ? `$${Number(position.take_profit).toFixed(2)}` : '--'}
                        </div>
                        <div className="text-red-600">
                          SL: {position.stop_loss ? `$${Number(position.stop_loss).toFixed(2)}` : '--'}
                        </div>
                      </div>

                      {/* Mobile Actions */}
                      <div className="flex gap-2 pt-2 border-t border-border">
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1 h-8 text-xs"
                          onClick={() => console.log('Market close position:', position.id)}
                        >
                          {t('trading.marketClose')}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1 h-8 text-xs"
                          onClick={() => console.log('Reverse position:', position.id)}
                        >
                          {t('trading.reverse')}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1 h-8 text-xs"
                          onClick={() => console.log('Edit TP/SL for position:', position.id)}
                        >
                          {t('trading.editTpSl')}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className={`${isMobile ? 'mb-4 w-full min-w-0' : 'mb-6 w-full'} max-w-full overflow-hidden`}>
      <CardContent className={`${isMobile ? 'p-3' : 'p-6'} flex flex-col gap-3`}>
        <div className={`flex ${isMobile ? 'flex-col gap-2' : 'justify-between items-center'} mb-2`}>
          <div className="flex items-center gap-2">
            <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium ${isPaperMode ? 'text-orange-600 dark:text-orange-400' : 'text-muted-foreground'}`}>
              {isPaperMode ? t('trading.paperTradingPositions') : t('dashboard.activePositions.title')}
            </h3>
            {isPaperMode && (
              <span className="text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded">
                {t('trading.paperTradingLabel')}
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {!isPaperMode && (
              <div className="flex border rounded">
                {(['all', 'app', 'external'] as const).map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`px-3 py-1 text-xs capitalize ${
                      activeTab === tab
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-background hover:bg-accent'
                    }`}
                  >
                    {t(`dashboard.activePositions.tabs.${tab}`)}
                  </button>
                ))}
              </div>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={() => fetchPositions(activeTab)}
              disabled={loading}
              aria-label="Refresh Active Positions"
            >
              <RefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              {t('dashboard.activePositions.refresh')}
            </Button>
          </div>
        </div>
        
        {loading ? (
          <div className="flex items-center justify-center h-24">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="text-destructive text-center py-4">
            <p className="font-medium">{t('dashboard.activePositions.errorLoading')}</p>
            <p className="text-xs">{error}</p>
            <Button
              size="sm"
              variant="outline"
              className="mt-2"
              onClick={() => fetchPositions(activeTab)}
            >
              {t('dashboard.activePositions.retry')}
            </Button>
          </div>
        ) : isPaperMode ? (
          // Paper mode positions (always show paper mode styling)
          <div className="space-y-6">
            {positions?.positions && positions.positions.length > 0 ? (
              renderPositions(positions.positions, t('trading.paperTradingPositions'), "paper")
            ) : (
              <div className="text-center py-12">
                <div className="text-muted-foreground mb-4">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-orange-100 dark:bg-orange-900/20 flex items-center justify-center">
                    <svg className="w-8 h-8 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <p className="text-lg font-medium mb-2 text-orange-600 dark:text-orange-400">{t('trading.noPaperPositions')}</p>
                  <p className="text-sm text-orange-500 dark:text-orange-400 max-w-md mx-auto">
                    {t('trading.enableAutoTradingPaper')}
                  </p>
                  <p className="text-xs text-orange-500 dark:text-orange-400 mt-2">{t('trading.paperTradingMode')}</p>
                </div>
              </div>
            )}
          </div>
        ) : activeTab === 'all' && positions ? (
          // Live mode positions
          <div className="space-y-6">
            {positions.app_positions && positions.app_positions.length > 0 &&
              renderPositions(positions.app_positions, t('dashboard.activePositions.positionTypes.appPositions'), "app")}
            {positions.external_positions && positions.external_positions.length > 0 &&
              renderPositions(positions.external_positions, t('dashboard.activePositions.positionTypes.externalPositions'), "external")}
            {(!positions.app_positions || positions.app_positions.length === 0) &&
             (!positions.external_positions || positions.external_positions.length === 0) && (
              <div className="text-center py-12">
                <div className="text-muted-foreground mb-4">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <p className="text-lg font-medium mb-2">{t('trading.youHaveNoPositions')}</p>
                  <p className="text-sm text-muted-foreground max-w-md mx-auto">
                    {t('trading.yourActivePositionsWillAppearHere')}
                  </p>
                </div>
                {positions.diagnostics && (
                  <div className="mt-6 p-4 bg-muted/30 rounded-lg max-w-md mx-auto">
                    <div className="text-xs space-y-2">
                      {positions.diagnostics.credential_status === "No active API credentials found" && (
                        <div className="flex items-center gap-2 text-amber-600">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <span>{t('dashboard.activePositions.addCredentialsWarning')}</span>
                        </div>
                      )}
                      {positions.diagnostics.external_error && (
                        <div className="flex items-center gap-2 text-red-600">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                          <span>{t('dashboard.activePositions.externalError', { error: positions.diagnostics.external_error })}</span>
                        </div>
                      )}
                      <div className="text-gray-500 text-center pt-2 border-t border-border">
                        {t('dashboard.activePositions.diagnostics', {
                          app: positions.diagnostics.app_trades_count || 0,
                          external: positions.diagnostics.external_positions_count || 0
                        })}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : positions && positions.positions ? (
          renderPositions(positions.positions, `${activeTab} Positions`, activeTab)
        ) : (
          <div className="text-center py-12">
            <div className="text-muted-foreground mb-4">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <p className="text-lg font-medium mb-2">{t('trading.youHaveNoPosition')}</p>
              <p className="text-sm text-muted-foreground max-w-md mx-auto">
                {activeTab === 'app' ? t('trading.noActiveAppPositions') :
                 activeTab === 'external' ? t('trading.noActiveExternalPositions') :
                 t('trading.yourActivePositionsWillAppear')}
              </p>
            </div>
            {positions?.diagnostics && (
              <div className="mt-6 p-4 bg-muted/30 rounded-lg max-w-md mx-auto">
                <div className="text-xs space-y-2">
                  {positions.diagnostics.credential_status === "No active API credentials found" && activeTab === 'external' && (
                    <div className="flex items-center gap-2 text-amber-600">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <span>{t('dashboard.activePositions.addCredentialsWarning')}</span>
                    </div>
                  )}
                  {positions.diagnostics.external_error && activeTab === 'external' && (
                    <div className="flex items-center gap-2 text-red-600">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                      <span>{t('dashboard.activePositions.externalErrorGeneric', { error: positions.diagnostics.external_error })}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Profit Share Summary Component
export function ProfitShareSummary() {
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const [profitData, setProfitData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProfitShareSummary();
  }, []);

  const fetchProfitShareSummary = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/trading/profit-share/summary', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProfitData(data);
      } else {
        console.error('Failed to fetch profit share summary');
      }
    } catch (error) {
      console.error('Error fetching profit share summary:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="text-center">{t('dashboard.profitShare.loading')}</div>
        </CardContent>
      </Card>
    );
  }

  if (!profitData) {
    return (
      <Card className="mb-6">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">{t('dashboard.profitShare.title')}</h3>
          <div className="text-center text-muted-foreground">
            {t('dashboard.profitShare.noData')}
          </div>
        </CardContent>
      </Card>
    );
  }

  const { tier_info, profit_analysis } = profitData;

  return (
    <Card className={`${isMobile ? 'mb-4 w-full min-w-0' : 'mb-6'} max-w-full overflow-hidden`}>
      <CardContent className={`${isMobile ? 'p-3' : 'p-6'}`}>
        <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold ${isMobile ? 'mb-3' : 'mb-4'}`}>{t('dashboard.profitShare.title')}</h3>

        <div className={`grid grid-cols-1 ${isMobile ? 'gap-3 mb-4' : 'md:grid-cols-3 gap-4 mb-6'}`}>
          {/* Balance Overview */}
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h4 className="font-medium text-sm text-muted-foreground mb-2">{t('dashboard.profitShare.balanceOverview')}</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">{t('dashboard.profitShare.initialBalance')}</span>
                <span className="font-medium">${profit_analysis.initial_balance.toFixed(6)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">{t('dashboard.profitShare.currentBalance')}</span>
                <span className="font-medium">${profit_analysis.current_balance.toFixed(6)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">{t('dashboard.profitShare.netDeposits')}</span>
                <span className="font-medium">${profit_analysis.net_deposits.toFixed(6)}</span>
              </div>
            </div>
          </div>

          {/* Profit Analysis */}
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h4 className="font-medium text-sm text-muted-foreground mb-2">{t('dashboard.profitShare.profitAnalysis')}</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">{t('dashboard.profitShare.tradingProfit')}</span>
                <span className={`font-medium ${profit_analysis.trading_profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${profit_analysis.trading_profit.toFixed(6)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">{t('dashboard.profitShare.trueProfit')}</span>
                <span className={`font-medium ${profit_analysis.true_profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${profit_analysis.true_profit.toFixed(6)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">{t('dashboard.profitShare.profitPercentage')}</span>
                <span className={`font-medium ${profit_analysis.profit_percentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {profit_analysis.profit_percentage.toFixed(2)}%
                </span>
              </div>
            </div>
          </div>

          {/* Profit Share Info */}
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h4 className="font-medium text-sm text-muted-foreground mb-2">{t('dashboard.profitShare.profitShareInfo')}</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">{t('dashboard.profitShare.tierRate', { tier: tier_info.current_tier })}</span>
                <span className="font-medium">{(tier_info.profit_share_rate * 100).toFixed(0)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">{t('dashboard.profitShare.amountOwed')}</span>
                <span className="font-medium text-orange-600">
                  ${tier_info.profit_share_owed.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">{t('dashboard.profitShare.status')}</span>
                <span className={`px-2 py-1 rounded text-xs ${
                  tier_info.payment_status === 'paid'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
                }`}>
                  {tier_info.payment_status}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Profit Status Alert */}
        <div className={`p-4 rounded-lg border ${
          profit_analysis.is_profitable
            ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
            : 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
        }`}>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              profit_analysis.is_profitable ? 'bg-green-500' : 'bg-yellow-500'
            }`}></div>
            <span className="font-medium">
              {profit_analysis.is_profitable
                ? t('dashboard.profitShare.profitable')
                : t('dashboard.profitShare.notProfitable')
              }
            </span>
          </div>
        </div>

        {tier_info.profit_share_owed > 0 && (
          <div className="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg dark:bg-orange-900/20 dark:border-orange-800">
            <div className="flex justify-between items-center">
              <div>
                <h5 className="font-medium text-orange-800 dark:text-orange-200">{t('dashboard.profitShare.paymentRequired')}</h5>
                <p className="text-sm text-orange-600 dark:text-orange-300">
                  {t('dashboard.profitShare.paymentDescription', { amount: tier_info.profit_share_owed.toFixed(2) })}
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/tier'}
                className="border-orange-300 text-orange-700 hover:bg-orange-100 dark:border-orange-700 dark:text-orange-300"
              >
                {t('dashboard.profitShare.payNow')}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Paginated trading history - mode-aware (paper vs live)
export function TradingHistory() {
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const [history, setHistory] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const [pagination, setPagination] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // Paper trading hook for mode awareness
  const { isPaperMode } = usePaperTrading();

  useEffect(() => {
    console.log(`[TradingHistory] useEffect triggered - isPaperMode: ${isPaperMode}, page: ${page}`);
    fetchTradingHistory();
  }, [page, isPaperMode]); // Auto-refresh when trading mode changes

  // Reset page when trading mode changes
  useEffect(() => {
    console.log(`[TradingHistory] Mode change detected - isPaperMode: ${isPaperMode}, resetting page to 1`);
    setPage(1);
    // Clear existing data immediately when mode changes
    setHistory([]);
    setPagination(null);
  }, [isPaperMode]);

  const fetchTradingHistory = async () => {
    console.log(`[TradingHistory] fetchTradingHistory called - isPaperMode: ${isPaperMode}, page: ${page}`);
    setLoading(true);

    // Clear existing history immediately when switching modes
    setHistory([]);
    setPagination(null);

    try {
      if (isPaperMode) {
        console.log('[TradingHistory] Fetching paper trading history...');
        // Fetch paper trading history
        const limit = 10; // Items per page
        const offset = (page - 1) * limit;
        const paperHistory = await paperTradingApi.getPaperTradingHistory(limit, offset);

        if (paperHistory.success) {
          console.log('[TradingHistory] Paper history data received:', paperHistory);
          setHistory(paperHistory.trades || []);
          setPagination(paperHistory.pagination);
        } else {
          console.error('[TradingHistory] Failed to fetch paper trading history');
          setHistory([]);
          setPagination(null);
        }
      } else {
        console.log(`[TradingHistory] Fetching live trading history for page: ${page}...`);
        // Fetch live trading history
        const response = await fetch(`/api/trading/history?page=${page}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('[TradingHistory] Live history data received:', data);
          setHistory(data.trades || []);
          setPagination(data.pagination);
        } else {
          console.error('[TradingHistory] Failed to fetch live trading history');
          setHistory([]);
          setPagination(null);
        }
      }
    } catch (error) {
      console.error('[TradingHistory] Error fetching trading history:', error);
      setHistory([]);
      setPagination(null);
    } finally {
      setLoading(false);
    }
  };

  const formatPnL = (pnl: number | null) => {
    if (pnl === null) return '--';
    const formatted = pnl.toFixed(2);
    return pnl >= 0 ? `+$${formatted}` : `-$${Math.abs(pnl).toFixed(2)}`;
  };

  const getPnLColor = (pnl: number | null) => {
    if (pnl === null) return 'text-muted-foreground';
    return pnl >= 0 ? 'text-green-600' : 'text-red-600';
  };

  return (
    <Card className={`${isMobile ? 'mb-4 w-full min-w-0' : 'mb-6'} max-w-full overflow-hidden`}>
      <CardContent className={`${isMobile ? 'p-3' : 'p-6'}`}>
        <div className={`flex ${isMobile ? 'flex-col gap-2' : 'justify-between items-center'} mb-4`}>
          <div className="flex items-center gap-2">
            <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium ${isPaperMode ? 'text-orange-600 dark:text-orange-400' : 'text-muted-foreground'}`}>
              {isPaperMode ? t('trading.paperTradingHistory') : t('dashboard.tradingHistory.title')}
            </h3>
            {isPaperMode && (
              <span className="text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded">
                {t('trading.paperTradingLabel')}
              </span>
            )}
          </div>
          <div className={`text-xs ${isPaperMode ? 'text-orange-500 dark:text-orange-400' : 'text-muted-foreground'}`}>
            {isPaperMode ? t('trading.simulatedTradingHistory') : t('dashboard.tradingHistory.description')}
          </div>
        </div>

        {loading ? (
          <div className="text-center py-4">{t('dashboard.tradingHistory.loading')}</div>
        ) : (
          <>
            <div className={`overflow-x-auto ${isMobile ? 'w-full min-w-0' : ''}`}>
              <table className={`w-full ${isMobile ? 'text-xs min-w-0' : 'text-sm'}`}>
                <thead>
                  <tr className="border-b">
                    <th className={`text-left ${isMobile ? 'p-1' : 'p-2'}`}>{t('dashboard.tradingHistory.headers.date')}</th>
                    <th className={`text-left ${isMobile ? 'p-1' : 'p-2'}`}>{t('dashboard.tradingHistory.headers.symbol')}</th>
                    <th className={`text-left ${isMobile ? 'p-1' : 'p-2'}`}>{t('dashboard.tradingHistory.headers.side')}</th>
                    <th className={`text-left ${isMobile ? 'p-1' : 'p-2'}`}>{t('dashboard.tradingHistory.headers.size')}</th>
                    <th className={`text-left ${isMobile ? 'p-1' : 'p-2'}`}>{t('dashboard.tradingHistory.headers.entry')}</th>
                    <th className={`text-left ${isMobile ? 'p-1' : 'p-2'}`}>{t('dashboard.tradingHistory.headers.exit')}</th>
                    <th className={`text-left ${isMobile ? 'p-1' : 'p-2'}`}>{t('dashboard.tradingHistory.headers.pnl')}</th>
                    <th className={`text-left ${isMobile ? 'p-1' : 'p-2'}`}>{t('dashboard.tradingHistory.headers.status')}</th>
                  </tr>
                </thead>
                <tbody>
                  {history.length > 0 ? (
                    history.map((trade, i) => (
                      <tr key={trade.id || i} className={`border-b ${isPaperMode ? 'hover:bg-orange-50 dark:hover:bg-orange-900/10' : 'hover:bg-gray-50 dark:hover:bg-gray-800'}`}>
                        <td className={`${isMobile ? 'p-1' : 'p-2'}`}>{trade.date}</td>
                        <td className={`${isMobile ? 'p-1' : 'p-2'} font-medium`}>{trade.symbol}</td>
                        <td className={`${isMobile ? 'p-1' : 'p-2'}`}>
                          <span className={`px-2 py-1 rounded text-xs ${
                            trade.side === 'LONG' || trade.side === 'BUY'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                          }`}>
                            {trade.side}
                          </span>
                        </td>
                        <td className={`${isMobile ? 'p-1' : 'p-2'}`}>{trade.size}</td>
                        <td className={`${isMobile ? 'p-1' : 'p-2'}`}>${trade.entry?.toFixed(2) || '--'}</td>
                        <td className={`${isMobile ? 'p-1' : 'p-2'}`}>${trade.exit?.toFixed(2) || '--'}</td>
                        <td className={`${isMobile ? 'p-1' : 'p-2'} font-medium ${getPnLColor(trade.pnl)}`}>
                          {formatPnL(trade.pnl)}
                        </td>
                        <td className={`${isMobile ? 'p-1' : 'p-2'}`}>
                          <div className="flex flex-col gap-1">
                            <span className={`px-2 py-1 rounded text-xs ${
                              trade.status === 'CLOSED'
                                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            }`}>
                              {trade.status}
                            </span>
                            {isPaperMode && (
                              <span className="text-xs text-orange-500 dark:text-orange-400">{t('trading.paperMode')}</span>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={8} className="text-center py-8 text-muted-foreground">
                        {t('dashboard.tradingHistory.noHistory')}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {pagination && pagination.pages > 1 && (
              <div className="flex justify-between items-center mt-4">
                <div className="text-sm text-muted-foreground">
                  {t('dashboard.tradingHistory.pagination', {
                    page: pagination.page,
                    pages: pagination.pages,
                    total: pagination.total
                  })}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page - 1)}
                    disabled={!pagination.has_prev}
                  >
                    {t('dashboard.tradingHistory.previous')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page + 1)}
                    disabled={!pagination.has_next}
                  >
                    {t('dashboard.tradingHistory.next')}
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}

// UI feedback for blocked users
export function BlockedUserAlert() {
  const { t } = useTranslation();
  const [blocked, setBlocked] = useState(false);
  const [owed, setOwed] = useState("");
  const [tier, setTier] = useState("");

  useEffect(() => {
    // Example: fetch from backend or context
    fetch("/api/trading/blocked_status", {
      credentials: 'include',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      },
    }).then(res => res.json()).then(data => {
      if (data.blocked) {
        setBlocked(true);
        setOwed(data.owed);
        setTier(data.tier);
      }
    });
  }, []);

  if (!blocked) return null;
  return (
    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
      <strong>{t('dashboard.blockedUser.title')}</strong> {t('dashboard.blockedUser.message', { tier, owed })}
    </div>
  );
}
