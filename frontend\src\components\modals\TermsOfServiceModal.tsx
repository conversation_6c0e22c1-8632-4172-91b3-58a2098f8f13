/**
 * Terms of Service Modal Component for DeepTrade
 *
 * Displays the Terms of Service in a modal dialog with proper scrolling,
 * accessibility features, and responsive design.
 * Always displays in English regardless of language selector setting.
 */

import React, { useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, FileText } from 'lucide-react';
import { Button } from '../ui/Button';
import { cn } from '@/lib/utils';

interface TermsOfServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export const TermsOfServiceModal: React.FC<TermsOfServiceModalProps> = ({
  isOpen,
  onClose,
  className,
}) => {
  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const modalContent = (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Modal */}
      <div
        className={cn(
          "relative bg-white dark:bg-gray-800 rounded-lg shadow-2xl",
          "w-full max-w-4xl max-h-[90vh] flex flex-col",
          "border border-gray-200 dark:border-gray-700",
          className
        )}
        role="dialog"
        aria-modal="true"
        aria-labelledby="terms-modal-title"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <FileText className="w-6 h-6 text-primary" />
            <h2 id="terms-modal-title" className="text-xl font-semibold text-gray-900 dark:text-white">
              Terms of Service
            </h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="rounded-full"
            aria-label="Close Terms of Service"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="prose prose-gray dark:prose-invert max-w-none">
            <div className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              Last updated: January 15, 2025
            </div>

            <h3>1. Acceptance of Terms</h3>
            <p>
              By accessing and using DeepTrade ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
            </p>

            <h3>2. Description of Service</h3>
            <p>
              DeepTrade is an AI-powered cryptocurrency trading platform that provides trading signals, automated portfolio management, and related financial services. The Service is provided "as is" and on an "as available" basis.
            </p>

            <h3>3. User Responsibilities</h3>
            <p>
              Users are responsible for:
            </p>
            <ul>
              <li>Maintaining the confidentiality of their account credentials</li>
              <li>All activities that occur under their account</li>
              <li>Ensuring compliance with applicable laws and regulations</li>
              <li>Understanding the risks associated with cryptocurrency trading</li>
            </ul>

            <h3>4. Risk Disclosure</h3>
            <p>
              <strong>IMPORTANT:</strong> Cryptocurrency trading involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results. You should carefully consider whether trading is suitable for you in light of your circumstances, knowledge, and financial resources.
            </p>

            <h3>5. Prohibited Activities</h3>
            <p>
              Users may not:
            </p>
            <ul>
              <li>Use the Service for any illegal or unauthorized purpose</li>
              <li>Attempt to gain unauthorized access to the Service or its related systems</li>
              <li>Interfere with or disrupt the Service or servers or networks connected to the Service</li>
              <li>Share account credentials with third parties</li>
            </ul>

            <h3>6. Intellectual Property</h3>
            <p>
              The Service and its original content, features, and functionality are and will remain the exclusive property of DeepTrade and its licensors. The Service is protected by copyright, trademark, and other laws.
            </p>

            <h3>7. Limitation of Liability</h3>
            <p>
              In no event shall DeepTrade, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your use of the Service.
            </p>

            <h3>8. Termination</h3>
            <p>
              We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms.
            </p>

            <h3>9. Changes to Terms</h3>
            <p>
              We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect.
            </p>

            <h3>10. Contact Information</h3>
            <p>
              If you have any questions about these Terms of Service, please contact us at:
              <br />
              Email: <EMAIL>
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <Button onClick={onClose} className="min-w-[100px]">
            Close
          </Button>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default TermsOfServiceModal;
