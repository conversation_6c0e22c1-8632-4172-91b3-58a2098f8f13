from abc import ABC, abstractmethod
from typing import Dict, <PERSON><PERSON>, Optional
import requests
import hmac
import hashlib
import time
from decimal import Decimal
# import logging

class ExchangeService(ABC):
    """Abstract base class for exchange integrations"""
    
    def __init__(self, api_key: str, secret_key: str, passphrase: Optional[str] = None):
        self.api_key = api_key
        self.secret_key = secret_key
        self.passphrase = passphrase
        self.session = requests.Session()
        # self.logger = logging.getLogger(__name__)
    
    @abstractmethod
    def validate_credentials(self) -> Tuple[bool, Dict]:
        """Validate API credentials and return permissions"""
        pass
    
    @abstractmethod
    def get_account_info(self) -> Dict:
        """Get account information"""
        pass
    
    @abstractmethod
    def get_balance(self, quote_asset: str = 'USDT') -> Decimal:
        """Get account balance for specified quote asset"""
        pass
    def get_open_positions(self) -> list:
        """Fetch open positions from the exchange. Default: returns empty list for base class."""
        return []
    
    def get_open_orders(self, symbol: str) -> list:
        """Fetch open orders for a specific symbol. Default: returns empty list for base class."""
        return []
    
    @abstractmethod
    def place_order(self, symbol: str, side: str, quantity: float, price: Optional[float] = None) -> Dict:
        """Place a trading order"""
        pass

    @abstractmethod
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol"""
        pass

    def set_margin_mode(self, symbol: str, margin_mode: str) -> bool:
        """Set margin mode for symbol (isolated/cross). Default implementation returns True."""
        return True

    def set_leverage(self, symbol: str, leverage: int) -> bool:
        """Set leverage for symbol. Default implementation returns True."""
        return True

    def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel a specific order. Default implementation returns True."""
        return True

    def cancel_all_orders(self, symbol: str) -> bool:
        """Cancel all open orders for a symbol. Default implementation returns True."""
        return True

class BinanceService(ExchangeService):
    """Binance exchange implementation"""

    def get_open_positions(self) -> list:
        """Fetch open positions from Binance Futures"""
        try:
            endpoint = '/fapi/v2/positionRisk'
            params = {'timestamp': int(time.time() * 1000), 'recvWindow': 5000}
            params = self.create_signed_params(params)
            response = self.session.get(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )
            if response.status_code != 200:
                # self.logger.error(f"Failed to fetch positions: {response.text}")
                return []
            positions = response.json()
            # Only return positions with nonzero positionAmt
            open_positions = [
                {
                    'symbol': pos['symbol'],
                    'side': 'LONG' if float(pos['positionAmt']) > 0 else 'SHORT',
                    'size': abs(float(pos['positionAmt'])),
                    'entry': float(pos['entryPrice']),
                    'pnl': float(pos['unRealizedProfit']),
                    'leverage': int(pos['leverage']),
                    'marginType': pos.get('marginType', ''),
                    'liquidation_price': float(pos['liquidationPrice']) if 'liquidationPrice' in pos and pos['liquidationPrice'] not in [None, '', 0, '0'] else None,
                    # Add take profit and stop loss if available
                    'take_profit': float(pos['takeProfitPrice']) if 'takeProfitPrice' in pos and pos['takeProfitPrice'] not in [None, '', 0, '0'] else None,
                    'stop_loss': float(pos['stopLossPrice']) if 'stopLossPrice' in pos and pos['stopLossPrice'] not in [None, '', 0, '0'] else None,
                }
                for pos in positions if float(pos['positionAmt']) != 0
            ]
            # Enhance positions with TP/SL from open orders
            enhanced_positions = []
            for pos in open_positions:
                try:
                    # Get open orders for this symbol to find TP/SL
                    orders = self.get_open_orders(pos['symbol'])
                    tp_order = None
                    sl_order = None
                    
                    # Find TP and SL orders for this position
                    for order in orders:
                        order_type = order.get('type', '')
                        order_side = order.get('side', '')
                        
                        # Check if this order is a TP order
                        if order_type in ['TAKE_PROFIT', 'TAKE_PROFIT_MARKET']:
                            # Make sure the order side matches the position (opposite side for closing)
                            if (pos['side'] == 'LONG' and order_side == 'SELL') or \
                               (pos['side'] == 'SHORT' and order_side == 'BUY'):
                                tp_order = order
                        
                        # Check if this order is a SL order
                        elif order_type in ['STOP', 'STOP_MARKET']:
                            # Make sure the order side matches the position (opposite side for closing)
                            if (pos['side'] == 'LONG' and order_side == 'SELL') or \
                               (pos['side'] == 'SHORT' and order_side == 'BUY'):
                                sl_order = order
                    
                    # Update position with TP/SL prices from orders
                    # Robust TP extraction: prefer stopPrice if price is 0 or missing
                    if tp_order:
                        tp_price = tp_order.get('price')
                        if not tp_price or float(tp_price) == 0:
                            tp_price = tp_order.get('stopPrice')
                        pos['take_profit'] = float(tp_price) if tp_price and float(tp_price) > 0 else None
                    else:
                        pos['take_profit'] = None

                    # Robust SL extraction: prefer stopPrice if price is 0 or missing
                    if sl_order:
                        sl_price = sl_order.get('stopPrice')
                        if not sl_price or float(sl_price) == 0:
                            sl_price = sl_order.get('price')
                        pos['stop_loss'] = float(sl_price) if sl_price and float(sl_price) > 0 else None
                    else:
                        pos['stop_loss'] = None
                    
                except Exception as e:
                    #self.logger.warning(f"Failed to fetch TP/SL for {pos['symbol']}: {str(e)}")
                    # Keep original TP/SL values if fetching orders fails
                    pass
                
                # DEBUG: Print the liquidation price for each outgoing position
                print(f"[DEBUG] Outgoing position for {pos['symbol']} ({pos['side']}): liquidation_price={pos.get('liquidation_price')}")
                enhanced_positions.append(pos)
            
            return enhanced_positions
        except Exception as e:
            # self.logger.error(f"Error fetching open positions: {str(e)}", exc_info=True)
            return []
    
    def get_open_orders(self, symbol: str) -> list:
        """Fetch open orders for a specific symbol from Binance Futures"""
        try:
            endpoint = '/fapi/v1/openOrders'
            params = {
                'symbol': symbol,
                'timestamp': int(time.time() * 1000),
                'recvWindow': 5000
            }
            params = self.create_signed_params(params)
            response = self.session.get(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )
            if response.status_code != 200:
                # self.logger.warning(f"Failed to fetch open orders for {symbol}: {response.text}")
                return []
            orders = response.json()
            # self.logger.info(f"[DEBUG] Open orders for {symbol}: {orders}")
            return orders
        except Exception as e:
            # self.logger.warning(f"Error fetching open orders for {symbol}: {str(e)}")
            return []

    
    BASE_URL = 'https://fapi.binance.com'
    
    def create_signed_params(self, params: Dict) -> Dict:
        """Create signed parameters for authenticated requests"""
        query_string = '&'.join([f'{k}={v}' for k, v in params.items()])
        signature = hmac.new(
            self.secret_key.encode('utf-8'), 
            query_string.encode('utf-8'), 
            hashlib.sha256
        ).hexdigest()
        params['signature'] = signature
        return params
    
    def validate_credentials(self) -> Tuple[bool, Dict]:
        """Validate Binance API credentials"""
        try:
            # Test with account endpoint
            endpoint = '/fapi/v2/account'
            timestamp = int(time.time() * 1000)
            
            params = {
                'timestamp': timestamp,
                'recvWindow': 5000
            }
            
            params = self.create_signed_params(params)
            
            response = self.session.get(
                self.BASE_URL + endpoint, 
                params=params, 
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Extract balance
                balance = 0.0
                for asset in data.get('assets', []):
                    if asset['asset'] == 'USDT':
                        balance = float(asset['walletBalance'])
                        break
                
                # For Binance Futures, canWithdraw is not reliable. Instead, we'll assume
                # withdrawal is not allowed through the API for security reasons
                can_trade = data.get('canTrade', False)
                can_withdraw = False  # Always False for security
                
                # Log the raw data for debugging
                # self.logger.debug(f"Binance API Response: {data}")
                
                permissions = {
                    'can_read': True,
                    'can_trade': can_trade,
                    'can_withdraw': can_withdraw,  # Always False for security
                    'balance': balance,
                    'account_type': 'FUTURES'  # Indicate this is a Futures account
                }
                
                return True, permissions
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get('msg', f'API Error: {response.status_code}')
                return False, {'error': error_msg}
                
        except requests.exceptions.RequestException as e:
            return False, {'error': f'Connection error: {str(e)}'}
        except Exception as e:
            return False, {'error': f'Validation error: {str(e)}'}
    
    def get_account_info(self) -> Dict:
        """Get Binance account information"""
        try:
            endpoint = '/fapi/v2/account'
            timestamp = int(time.time() * 1000)
            
            params = {
                'timestamp': timestamp,
                'recvWindow': 5000
            }
            
            params = self.create_signed_params(params)
            
            response = self.session.get(
                self.BASE_URL + endpoint, 
                params=params, 
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )
            
            return response.json() if response.status_code == 200 else {}
        except Exception as e:
            # self.logger.error(f"Error getting account info: {str(e)}")
            return {}
    
    def get_balance(self, quote_asset: str = 'USDT') -> Decimal:
        """Get balance for specified quote asset from Binance Futures"""
        try:
            # First get account balance
            endpoint = '/fapi/v2/balance'
            params = {'timestamp': int(time.time() * 1000), 'recvWindow': 5000}
            params = self.create_signed_params(params)

            response = self.session.get(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )

            if response.status_code != 200:
                # self.logger.error(f"Failed to fetch balance: {response.text}")
                return Decimal('0')

            # Find balance for specified quote asset
            for asset in response.json():
                if asset.get('asset') == quote_asset.upper():
                    try:
                        return Decimal(asset.get('balance', '0')).quantize(Decimal('0.********'))
                    except (ValueError, TypeError) as e:
                        # self.logger.error(f"Error parsing {quote_asset} balance: {str(e)}")
                        return Decimal('0')

            # self.logger.error(f"{quote_asset} balance not found in response")
            return Decimal('0')

        except requests.exceptions.RequestException as e:
            # self.logger.error(f"Network error fetching balance: {str(e)}")
            return Decimal('0')
        except Exception as e:
            # self.logger.error(f"Unexpected error in get_balance: {str(e)}", exc_info=True)
            return Decimal('0')
    
    def place_order(self, symbol: str, side: str, quantity: float, price: Optional[float] = None) -> Dict:
        """Place order on Binance"""
        endpoint = '/fapi/v1/order'
        timestamp = int(time.time() * 1000)
        
        params = {
            'symbol': symbol,
            'side': side.upper(),
            'type': 'MARKET' if price is None else 'LIMIT',
            'quantity': quantity,
            'timestamp': timestamp,
            'recvWindow': 5000
        }
        
        if price is not None:
            params['price'] = price
            params['timeInForce'] = 'GTC'
        
        params = self.create_signed_params(params)
        
        response = self.session.post(
            self.BASE_URL + endpoint,
            params=params,
            headers={'X-MBX-APIKEY': self.api_key},
            timeout=10
        )
        
        return response.json()

    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol from Binance"""
        try:
            endpoint = '/fapi/v1/ticker/price'
            params = {'symbol': symbol.upper()}

            response = self.session.get(
                self.BASE_URL + endpoint,
                params=params,
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                return float(data['price'])
            else:
                return None

        except Exception as e:
            # self.logger.error(f"Error getting current price for {symbol}: {str(e)}")
            return None

    def set_margin_mode(self, symbol: str, margin_mode: str) -> bool:
        """Set margin mode for symbol on Binance Futures"""
        try:
            endpoint = '/fapi/v1/marginType'
            timestamp = int(time.time() * 1000)

            # Binance uses ISOLATED or CROSSED
            binance_margin_mode = 'ISOLATED' if margin_mode.lower() == 'isolated' else 'CROSSED'

            params = {
                'symbol': symbol.upper(),
                'marginType': binance_margin_mode,
                'timestamp': timestamp,
                'recvWindow': 5000
            }

            params = self.create_signed_params(params)

            response = self.session.post(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )

            if response.status_code == 200:
                return True
            else:
                # Log error but don't fail the trade
                print(f"Warning: Failed to set margin mode for {symbol}: {response.text}")
                return False

        except Exception as e:
            print(f"Error setting margin mode for {symbol}: {str(e)}")
            return False

    def set_leverage(self, symbol: str, leverage: int) -> bool:
        """Set leverage for symbol on Binance Futures"""
        try:
            endpoint = '/fapi/v1/leverage'
            timestamp = int(time.time() * 1000)

            params = {
                'symbol': symbol.upper(),
                'leverage': leverage,
                'timestamp': timestamp,
                'recvWindow': 5000
            }

            params = self.create_signed_params(params)

            response = self.session.post(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )

            if response.status_code == 200:
                return True
            else:
                # Log error but don't fail the trade
                print(f"Warning: Failed to set leverage for {symbol}: {response.text}")
                return False

        except Exception as e:
            print(f"Error setting leverage for {symbol}: {str(e)}")
            return False

    def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel a specific order on Binance Futures"""
        try:
            endpoint = '/fapi/v1/order'
            timestamp = int(time.time() * 1000)

            params = {
                'symbol': symbol.upper(),
                'orderId': order_id,
                'timestamp': timestamp,
                'recvWindow': 5000
            }

            params = self.create_signed_params(params)

            response = self.session.delete(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )

            if response.status_code == 200:
                return True
            else:
                print(f"Warning: Failed to cancel order {order_id} for {symbol}: {response.text}")
                return False

        except Exception as e:
            print(f"Error canceling order {order_id} for {symbol}: {str(e)}")
            return False

    def cancel_all_orders(self, symbol: str) -> bool:
        """Cancel all open orders for a symbol on Binance Futures"""
        try:
            endpoint = '/fapi/v1/allOpenOrders'
            timestamp = int(time.time() * 1000)

            params = {
                'symbol': symbol.upper(),
                'timestamp': timestamp,
                'recvWindow': 5000
            }

            params = self.create_signed_params(params)

            response = self.session.delete(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )

            if response.status_code == 200:
                canceled_orders = response.json()
                print(f"Successfully canceled {len(canceled_orders)} orders for {symbol}")
                return True
            else:
                print(f"Warning: Failed to cancel all orders for {symbol}: {response.text}")
                return False

        except Exception as e:
            print(f"Error canceling all orders for {symbol}: {str(e)}")
            return False


class BinanceUSService(BinanceService):
    """Binance US exchange implementation - inherits from BinanceService but uses different base URL"""

    BASE_URL = 'https://api.binance.us'  # Binance US API endpoint

    def validate_credentials(self) -> Tuple[bool, Dict]:
        """Validate Binance US API credentials"""
        try:
            # Use spot account endpoint for Binance US (no futures)
            endpoint = '/api/v3/account'
            timestamp = int(time.time() * 1000)

            params = {
                'timestamp': timestamp,
                'recvWindow': 5000
            }

            params = self.create_signed_params(params)

            response = self.session.get(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()

                # Extract balance
                balance = 0.0
                for asset in data.get('balances', []):
                    if asset['asset'] == 'USD':  # Binance US uses USD
                        balance = float(asset['free'])
                        break

                permissions = {
                    'can_read': True,
                    'can_trade': data.get('canTrade', False),
                    'can_withdraw': False,  # Always False for security
                    'balance': balance,
                    'account_type': 'SPOT'  # Binance US is spot trading
                }

                return True, permissions
            else:
                return False, {'error': f'API Error: {response.status_code}'}

        except Exception as e:
            return False, {'error': f'Connection failed: {str(e)}'}

    def get_balance(self, quote_asset: str = 'USD') -> Decimal:
        """Get balance for specified quote asset from Binance US (spot trading)"""
        try:
            endpoint = '/api/v3/account'
            params = {'timestamp': int(time.time() * 1000), 'recvWindow': 5000}
            params = self.create_signed_params(params)

            response = self.session.get(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )

            if response.status_code != 200:
                return Decimal('0')

            data = response.json()

            # Find balance for specified quote asset
            for asset in data.get('balances', []):
                if asset.get('asset') == quote_asset.upper():
                    try:
                        return Decimal(asset.get('free', '0')).quantize(Decimal('0.********'))
                    except (ValueError, TypeError):
                        return Decimal('0')

            return Decimal('0')

        except Exception:
            return Decimal('0')

    def place_order(self, symbol: str, side: str, quantity: float, price: Optional[float] = None) -> Dict:
        """Place order on Binance US (spot trading)"""
        endpoint = '/api/v3/order'
        timestamp = int(time.time() * 1000)

        params = {
            'symbol': symbol,
            'side': side.upper(),
            'type': 'MARKET' if price is None else 'LIMIT',
            'quantity': quantity,
            'timestamp': timestamp,
            'recvWindow': 5000
        }

        if price is not None:
            params['price'] = price
            params['timeInForce'] = 'GTC'

        params = self.create_signed_params(params)

        response = self.session.post(
            self.BASE_URL + endpoint,
            params=params,
            headers={'X-MBX-APIKEY': self.api_key},
            timeout=10
        )

        return response.json()

    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol from Binance US"""
        try:
            endpoint = '/api/v3/ticker/price'
            params = {'symbol': symbol.upper()}

            response = self.session.get(
                self.BASE_URL + endpoint,
                params=params,
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                return float(data['price'])
            else:
                return None

        except Exception:
            return None

    def get_open_orders(self, symbol: str) -> list:
        """Fetch open orders for a specific symbol from Binance US"""
        try:
            endpoint = '/api/v3/openOrders'
            params = {
                'symbol': symbol,
                'timestamp': int(time.time() * 1000),
                'recvWindow': 5000
            }
            params = self.create_signed_params(params)
            response = self.session.get(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )
            if response.status_code != 200:
                return []
            return response.json()
        except Exception:
            return []

    def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel a specific order on Binance US"""
        try:
            endpoint = '/api/v3/order'
            timestamp = int(time.time() * 1000)

            params = {
                'symbol': symbol.upper(),
                'orderId': order_id,
                'timestamp': timestamp,
                'recvWindow': 5000
            }

            params = self.create_signed_params(params)

            response = self.session.delete(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )

            return response.status_code == 200

        except Exception:
            return False

    def cancel_all_orders(self, symbol: str) -> bool:
        """Cancel all open orders for a symbol on Binance US"""
        try:
            endpoint = '/api/v3/openOrders'
            timestamp = int(time.time() * 1000)

            params = {
                'symbol': symbol.upper(),
                'timestamp': timestamp,
                'recvWindow': 5000
            }

            params = self.create_signed_params(params)

            response = self.session.delete(
                self.BASE_URL + endpoint,
                params=params,
                headers={'X-MBX-APIKEY': self.api_key},
                timeout=10
            )

            if response.status_code == 200:
                canceled_orders = response.json()
                print(f"Successfully canceled {len(canceled_orders)} orders for {symbol} on Binance US")
                return True
            else:
                return False

        except Exception:
            return False


class KrakenService(ExchangeService):
    """Kraken exchange implementation for futures trading"""

    BASE_URL = 'https://futures.kraken.com'

    def create_signed_params(self, params: Dict, endpoint: str) -> Dict:
        """Create signed parameters for Kraken authenticated requests"""
        import base64
        import hashlib
        import hmac

        # Kraken uses a different signing method
        postdata = '&'.join([f'{k}={v}' for k, v in params.items()])
        encoded = (str(params.get('nonce', int(time.time() * 1000))) + postdata).encode()
        message = endpoint.encode() + hashlib.sha256(encoded).digest()

        signature = hmac.new(
            base64.b64decode(self.secret_key),
            message,
            hashlib.sha512
        )

        params['signature'] = base64.b64encode(signature.digest()).decode()
        return params

    def validate_credentials(self) -> Tuple[bool, Dict]:
        """Validate Kraken API credentials"""
        try:
            endpoint = '/derivatives/api/v3/accounts'
            params = {'nonce': int(time.time() * 1000)}
            params = self.create_signed_params(params, endpoint)

            response = self.session.post(
                self.BASE_URL + endpoint,
                data=params,
                headers={'API-Key': self.api_key},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('result') == 'success':
                    accounts = data.get('accounts', {})
                    balance = 0.0

                    # Get USD balance
                    if 'cash' in accounts:
                        balance = float(accounts['cash'].get('usd', 0))

                    permissions = {
                        'can_read': True,
                        'can_trade': True,  # Assume trading is allowed if credentials are valid
                        'can_withdraw': False,  # Always False for security
                        'balance': balance,
                        'account_type': 'FUTURES'
                    }

                    return True, permissions
                else:
                    return False, {'error': data.get('error', 'Unknown error')}
            else:
                return False, {'error': f'API Error: {response.status_code}'}

        except Exception as e:
            return False, {'error': f'Connection failed: {str(e)}'}

    def get_account_info(self) -> Dict:
        """Get Kraken account information"""
        try:
            endpoint = '/derivatives/api/v3/accounts'
            params = {'nonce': int(time.time() * 1000)}
            params = self.create_signed_params(params, endpoint)

            response = self.session.post(
                self.BASE_URL + endpoint,
                data=params,
                headers={'API-Key': self.api_key},
                timeout=10
            )

            return response.json() if response.status_code == 200 else {}
        except Exception:
            return {}

    def get_balance(self, quote_asset: str = 'USD') -> Decimal:
        """Get balance for specified quote asset from Kraken Futures"""
        try:
            endpoint = '/derivatives/api/v3/accounts'
            params = {'nonce': int(time.time() * 1000)}
            params = self.create_signed_params(params, endpoint)

            response = self.session.post(
                self.BASE_URL + endpoint,
                data=params,
                headers={'API-Key': self.api_key},
                timeout=10
            )

            if response.status_code != 200:
                return Decimal('0')

            data = response.json()
            if data.get('result') != 'success':
                return Decimal('0')

            accounts = data.get('accounts', {})
            if 'cash' in accounts:
                balance = accounts['cash'].get(quote_asset.lower(), 0)
                return Decimal(str(balance)).quantize(Decimal('0.********'))

            return Decimal('0')

        except Exception:
            return Decimal('0')

    def place_order(self, symbol: str, side: str, quantity: float, price: Optional[float] = None) -> Dict:
        """Place order on Kraken Futures"""
        endpoint = '/derivatives/api/v3/sendorder'

        params = {
            'orderType': 'mkt' if price is None else 'lmt',
            'symbol': symbol,
            'side': side.lower(),
            'size': quantity,
            'nonce': int(time.time() * 1000)
        }

        if price is not None:
            params['limitPrice'] = price

        params = self.create_signed_params(params, endpoint)

        response = self.session.post(
            self.BASE_URL + endpoint,
            data=params,
            headers={'API-Key': self.api_key},
            timeout=10
        )

        return response.json()

    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol from Kraken"""
        try:
            endpoint = '/derivatives/api/v3/tickers'

            response = self.session.get(
                self.BASE_URL + endpoint,
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('result') == 'success':
                    tickers = data.get('tickers', [])
                    for ticker in tickers:
                        if ticker.get('symbol') == symbol:
                            return float(ticker.get('last', 0))

            return None

        except Exception:
            return None

    def get_open_orders(self, symbol: str) -> list:
        """Fetch open orders for a specific symbol from Kraken"""
        try:
            endpoint = '/derivatives/api/v3/openorders'
            params = {'nonce': int(time.time() * 1000)}
            params = self.create_signed_params(params, endpoint)

            response = self.session.post(
                self.BASE_URL + endpoint,
                data=params,
                headers={'API-Key': self.api_key},
                timeout=10
            )

            if response.status_code != 200:
                return []

            data = response.json()
            if data.get('result') != 'success':
                return []

            orders = data.get('openOrders', [])
            # Filter orders by symbol
            return [order for order in orders if order.get('symbol') == symbol]

        except Exception:
            return []

    def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel a specific order on Kraken"""
        try:
            endpoint = '/derivatives/api/v3/cancelorder'
            params = {
                'order_id': order_id,
                'nonce': int(time.time() * 1000)
            }
            params = self.create_signed_params(params, endpoint)

            response = self.session.post(
                self.BASE_URL + endpoint,
                data=params,
                headers={'API-Key': self.api_key},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                return data.get('result') == 'success'

            return False

        except Exception:
            return False

    def cancel_all_orders(self, symbol: str) -> bool:
        """Cancel all open orders for a symbol on Kraken"""
        try:
            # First get all open orders for the symbol
            open_orders = self.get_open_orders(symbol)

            if not open_orders:
                return True

            # Cancel each order individually
            success_count = 0
            for order in open_orders:
                order_id = order.get('order_id')
                if order_id and self.cancel_order(symbol, order_id):
                    success_count += 1

            print(f"Successfully canceled {success_count}/{len(open_orders)} orders for {symbol} on Kraken")
            return success_count == len(open_orders)

        except Exception:
            return False


class BitsoService(ExchangeService):
    """Bitso exchange implementation"""

    BASE_URL = 'https://api.bitso.com'

    def create_signed_params(self, params: Dict, method: str, endpoint: str) -> Dict:
        """Create signed parameters for Bitso authenticated requests"""
        import json

        nonce = str(int(time.time() * 1000))

        if method.upper() == 'GET':
            message = nonce + method.upper() + endpoint
        else:
            json_payload = json.dumps(params) if params else ''
            message = nonce + method.upper() + endpoint + json_payload

        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return {
            'Authorization': f'Bitso {self.api_key}:{nonce}:{signature}',
            'Content-Type': 'application/json'
        }

    def validate_credentials(self) -> Tuple[bool, Dict]:
        """Validate Bitso API credentials"""
        try:
            endpoint = '/v3/account_status'
            headers = self.create_signed_params({}, 'GET', endpoint)

            response = self.session.get(
                self.BASE_URL + endpoint,
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    payload = data.get('payload', {})

                    permissions = {
                        'can_read': True,
                        'can_trade': payload.get('trading_enabled', False),
                        'can_withdraw': False,  # Always False for security
                        'balance': 0.0,  # Will be fetched separately
                        'account_type': 'SPOT'  # Bitso is primarily spot trading
                    }

                    return True, permissions
                else:
                    return False, {'error': data.get('error', {}).get('message', 'Unknown error')}
            else:
                return False, {'error': f'API Error: {response.status_code}'}

        except Exception as e:
            return False, {'error': f'Connection failed: {str(e)}'}

    def get_account_info(self) -> Dict:
        """Get Bitso account information"""
        try:
            endpoint = '/v3/account_status'
            headers = self.create_signed_params({}, 'GET', endpoint)

            response = self.session.get(
                self.BASE_URL + endpoint,
                headers=headers,
                timeout=10
            )

            return response.json() if response.status_code == 200 else {}
        except Exception:
            return {}

    def get_balance(self, quote_asset: str = 'USD') -> Decimal:
        """Get balance for specified quote asset from Bitso"""
        try:
            endpoint = '/v3/balance'
            headers = self.create_signed_params({}, 'GET', endpoint)

            response = self.session.get(
                self.BASE_URL + endpoint,
                headers=headers,
                timeout=10
            )

            if response.status_code != 200:
                return Decimal('0')

            data = response.json()
            if not data.get('success'):
                return Decimal('0')

            balances = data.get('payload', {}).get('balances', [])
            for balance in balances:
                if balance.get('currency', '').upper() == quote_asset.upper():
                    return Decimal(str(balance.get('available', '0'))).quantize(Decimal('0.********'))

            return Decimal('0')

        except Exception:
            return Decimal('0')

    def place_order(self, symbol: str, side: str, quantity: float, price: Optional[float] = None) -> Dict:
        """Place order on Bitso"""
        endpoint = '/v3/orders'

        # Convert symbol format (e.g., BTCMXN -> btc_mxn)
        if len(symbol) >= 6:
            base = symbol[:-3].lower()
            quote = symbol[-3:].lower()
            book = f"{base}_{quote}"
        else:
            book = symbol.lower()

        params = {
            'book': book,
            'side': side.lower(),
            'type': 'market' if price is None else 'limit',
            'major': str(quantity)  # Bitso uses 'major' for quantity
        }

        if price is not None:
            params['price'] = str(price)

        headers = self.create_signed_params(params, 'POST', endpoint)

        response = self.session.post(
            self.BASE_URL + endpoint,
            json=params,
            headers=headers,
            timeout=10
        )

        return response.json()

    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol from Bitso"""
        try:
            # Convert symbol format
            if len(symbol) >= 6:
                base = symbol[:-3].lower()
                quote = symbol[-3:].lower()
                book = f"{base}_{quote}"
            else:
                book = symbol.lower()

            endpoint = f'/v3/ticker?book={book}'

            response = self.session.get(
                self.BASE_URL + endpoint,
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    payload = data.get('payload')
                    return float(payload.get('last', 0))

            return None

        except Exception:
            return None

    def get_open_orders(self, symbol: str) -> list:
        """Fetch open orders for a specific symbol from Bitso"""
        try:
            # Convert symbol format
            if len(symbol) >= 6:
                base = symbol[:-3].lower()
                quote = symbol[-3:].lower()
                book = f"{base}_{quote}"
            else:
                book = symbol.lower()

            endpoint = f'/v3/open_orders?book={book}'
            headers = self.create_signed_params({}, 'GET', endpoint)

            response = self.session.get(
                self.BASE_URL + endpoint,
                headers=headers,
                timeout=10
            )

            if response.status_code != 200:
                return []

            data = response.json()
            if not data.get('success'):
                return []

            return data.get('payload', [])

        except Exception:
            return []

    def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel a specific order on Bitso"""
        try:
            endpoint = f'/v3/orders/{order_id}'
            headers = self.create_signed_params({}, 'DELETE', endpoint)

            response = self.session.delete(
                self.BASE_URL + endpoint,
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                return data.get('success', False)

            return False

        except Exception:
            return False

    def cancel_all_orders(self, symbol: str) -> bool:
        """Cancel all open orders for a symbol on Bitso"""
        try:
            # First get all open orders for the symbol
            open_orders = self.get_open_orders(symbol)

            if not open_orders:
                return True

            # Cancel each order individually
            success_count = 0
            for order in open_orders:
                order_id = order.get('oid')
                if order_id and self.cancel_order(symbol, order_id):
                    success_count += 1

            print(f"Successfully canceled {success_count}/{len(open_orders)} orders for {symbol} on Bitso")
            return success_count == len(open_orders)

        except Exception:
            return False

# Factory function to get appropriate service

# Factory function to get appropriate service
def get_exchange_service(exchange, api_key: str, secret_key: str, passphrase: Optional[str] = None) -> ExchangeService:
    """Factory function to create exchange service instances"""
    # Accepts either Enum or string for exchange
    if hasattr(exchange, "value"):
        exchange_str = exchange.value
    else:
        exchange_str = str(exchange)

    # Convert to lowercase for comparison but keep original for error messages
    exchange_lower = exchange_str.lower()
    exchange_upper = exchange_str.upper()

    # Map of supported exchange names to their service classes
    EXCHANGE_SERVICES = {
        'binance': BinanceService,
        'binance_us': BinanceUSService,
        'kraken': KrakenService,
        'bitso': BitsoService,
    }

    # Find the matching service (case insensitive)
    service_class = None
    for name, service in EXCHANGE_SERVICES.items():
        if exchange_lower == name.lower():
            service_class = service
            break

    if service_class:
        return service_class(api_key, secret_key, passphrase)
    else:
        raise ValueError(f"Unsupported exchange: {exchange_str}. Supported exchanges: {', '.join(EXCHANGE_SERVICES.keys())}")