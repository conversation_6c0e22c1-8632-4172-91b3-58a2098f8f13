import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error
from typing import Dict, Tuple, Optional
from datetime import datetime
import logging

class TradingSignalGenerator:
    """Core trading signal generation logic extracted from trade.py"""
    
    def __init__(self, user_id: str, exchange_service, admin_monitoring_mode: bool = False):
        self.user_id = user_id
        self.exchange_service = exchange_service
        self.admin_monitoring_mode = admin_monitoring_mode
        self.logger = logging.getLogger(__name__)
    
    def generate_signals(self, symbol: str = 'BTCUSDT', timeframe: str = '1h') -> Dict:
        """Generate trading signals for a symbol"""
        from app.models.user_tier_status import UserTierStatus
        from app.models.user import User
        try:
            # Check if user has auto-trading enabled
            user = User.query.get(self.user_id)
            if not user:
                self.logger.error(f"[SIGNAL_GEN_ERROR] User {self.user_id} not found")
                return {
                    'error': 'User not found',
                    'blocked': True
                }

            # Allow signal generation for paper trading mode even if auto-trading is disabled
            # Also allow in admin monitoring mode for system monitoring purposes
            if not self.admin_monitoring_mode and not user.auto_trading_enabled and not user.paper_trading_mode:
                return {
                    'message': 'Auto-trading is disabled. Enable it in your settings to receive trading signals.',
                    'auto_trading_enabled': False,
                    'blocked': False
                }



            # Log if paper trading mode is generating signals
            if user.paper_trading_mode:
                self.logger.info(f"[SIGNAL_GEN_PAPER] Generating signals for paper trading mode for user {self.user_id}")

            # Enforce tier payment status
            user_tier = UserTierStatus.query.filter_by(user_id=self.user_id).first()
            if user_tier and user_tier.payment_status == 'unpaid' and user_tier.profit_share_owed > 0:
                self.logger.warning(f"[SIGNAL_GEN_BLOCKED] User {self.user_id} blocked due to unpaid profit share.")
                return {
                    'error': 'Trading signals blocked: Unpaid profit share for your tier. Please settle your balance to continue.',
                    'blocked': True,
                    'owed': str(user_tier.profit_share_owed),
                    'tier': user_tier.get_current_tier()
                }

            # Fetch market data
            market_data = self._fetch_market_data(symbol, timeframe)

            if market_data is None:
                return {'error': 'Market data fetch failed - service unavailable'}

            data_length = len(market_data) if market_data is not None else 0

            if data_length < 100:
                return {'error': f'Insufficient market data: {data_length} records (need 100+)'}

            # Generate ML forecast
            forecast_data = self._get_ml_forecast(market_data, symbol)
            if forecast_data is None:
                return {'error': 'Failed to generate ML forecast'}

            forecast, highest_price, lowest_price, forecast_result = forecast_data

            # Get swing points from the forecast result if available, otherwise calculate them
            swing_points = {
                'swing_high': forecast_result.get('swing_high'),
                'swing_low': forecast_result.get('swing_low')
            }

            # If swing points weren't in the forecast result, calculate them
            if swing_points['swing_high'] is None or swing_points['swing_low'] is None:
                calculated_swings = self._find_swing_points(market_data)
                swing_points['swing_high'] = swing_points['swing_high'] or calculated_swings.get('swing_high')
                swing_points['swing_low'] = swing_points['swing_low'] or calculated_swings.get('swing_low')

            # Calculate Heikin-Ashi
            heikin_ashi = self._calculate_heikin_ashi(market_data)

            # Generate trading signal
            signal_data = self._analyze_trading_conditions(
                market_data, forecast, swing_points, heikin_ashi,
                highest_price, lowest_price
            )

            if isinstance(signal_data, dict) and 'error' in signal_data:
                return signal_data

            return signal_data
            
        except Exception as e:
            return {'error': f'Signal generation exception: {str(e)}'}
    
    def _fetch_market_data(self, symbol: str, timeframe: str, lookback: int = 1000) -> Optional[pd.DataFrame]:
        """Fetch historical market data (extracted from trade.py fetch_binance_futures_data)"""
        try:
            try:
                from app.services.market_data import ml_service
            except (ImportError, Exception):
                return None

            # Check if ml_service has the expected attributes
            if not hasattr(ml_service, 'market_data'):
                return None

            if not hasattr(ml_service.market_data, 'get_klines'):
                return None

            # Use existing ML service to get market data
            klines = ml_service.market_data.get_klines(symbol, timeframe, lookback)

            if not klines:
                return None
            
            # Convert to pandas DataFrame similar to trade.py format
            data = []
            for kline in klines:
                try:
                    data.append({
                        'timestamp': kline['timestamp'],
                        'open': float(kline['open']),
                        'high': float(kline['high']),
                        'low': float(kline['low']),
                        'close': float(kline['close']),
                        'volume': float(kline['volume'])
                    })
                except (KeyError, ValueError, TypeError):
                    continue

            if not data:
                return None

            df = pd.DataFrame(data)

            # Convert timestamp to datetime index
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            return df

        except Exception:
            return None
    
    def _get_ml_forecast(self, market_data: pd.DataFrame, symbol: str) -> Optional[Tuple]:
        """Generate ML forecast using the ensemble model from ml_service"""
        try:
            if market_data is None or len(market_data) == 0:
                return None

            if 'close' not in market_data.columns:
                return None

            # Get the current price
            current_price = float(market_data['close'].iloc[-1])

            # Find swing points from the market data
            swing_points = self._find_swing_points(market_data)

            # Use the same forecast model as the chart plotly
            from app.services.market_data import ml_service
            forecast_result = ml_service.generate_ensemble_forecast(symbol, '1h', 72)

            if not forecast_result or 'forecast' not in forecast_result:
                return None

            # Extract forecast values from the response
            forecast_values = forecast_result['forecast']
            if not isinstance(forecast_values, (list, np.ndarray)) or len(forecast_values) == 0:
                return None
                
            # Convert to numpy array if it's a list
            if isinstance(forecast_values, list):
                forecast_values = np.array(forecast_values)
                
            # Get highest and lowest prices
            highest_price = float(np.max(forecast_values))
            lowest_price = float(np.min(forecast_values))
            
            # Add swing points to the forecast result if they exist
            if swing_points and 'swing_high' in swing_points and 'swing_low' in swing_points:
                forecast_result['swing_high'] = swing_points['swing_high']
                forecast_result['swing_low'] = swing_points['swing_low']

            return forecast_values, highest_price, lowest_price, forecast_result

        except Exception:
            return None
    
    def _find_swing_points(self, data: pd.DataFrame, max_look_back: int = 24) -> Dict:
        """Find swing high and low points (extracted from trade.py)"""
        last_swing_high = None
        last_swing_low = None
        
        try:
            for i in range(1, len(data) - 1):
                look_back_range = min(i, max_look_back)
                
                # Check for swing high
                prev_highs = data['high'].iloc[i - look_back_range:i]
                next_high = data['high'].iloc[i + 1]
                
                if data['high'].iloc[i] > np.max(prev_highs) and data['high'].iloc[i] > next_high:
                    last_swing_high = float(data['high'].iloc[i])
                
                # Check for swing low
                prev_lows = data['low'].iloc[i - look_back_range:i]
                next_low = data['low'].iloc[i + 1]
                
                if data['low'].iloc[i] < np.min(prev_lows) and data['low'].iloc[i] < next_low:
                    last_swing_low = float(data['low'].iloc[i])
            
            return {
                'swing_high': last_swing_high,
                'swing_low': last_swing_low
            }
            
        except Exception as e:
            self.logger.error(f"Error finding swing points: {str(e)}")
            return {'swing_high': None, 'swing_low': None}
    
    def _calculate_heikin_ashi(self, data: pd.DataFrame) -> Dict:
        """Calculate Heikin-Ashi values (extracted from trade.py)"""
        try:
            ha_data = data.copy()
            
            # Calculate HA Close
            ha_data['HA_Close'] = (
                ha_data['open'].astype(float) + 
                ha_data['high'].astype(float) + 
                ha_data['low'].astype(float) + 
                ha_data['close'].astype(float)
            ) / 4
            
            # Initialize HA Open
            ha_data['HA_Open'] = ha_data['open'].astype(float)
            
            # Calculate HA Open
            for i in range(1, len(ha_data)):
                ha_data.loc[ha_data.index[i], 'HA_Open'] = float(
                    (float(ha_data['HA_Open'].iloc[i-1]) + float(ha_data['HA_Close'].iloc[i-1])) / 2
                )
            
            # Calculate HA High and Low
            ha_data['HA_High'] = ha_data[['high', 'HA_Open', 'HA_Close']].astype(float).max(axis=1)
            ha_data['HA_Low'] = ha_data[['low', 'HA_Open', 'HA_Close']].astype(float).min(axis=1)
            
            # Get current and previous values
            current_ha_close = float(ha_data['HA_Close'].iloc[-1])
            current_ha_open = float(ha_data['HA_Open'].iloc[-1])
            previous_ha_close = float(ha_data['HA_Close'].iloc[-2])
            previous_ha_open = float(ha_data['HA_Open'].iloc[-2])
            
            ha_color = "green" if current_ha_close > current_ha_open else "red"
            prev_ha_color = "green" if previous_ha_close > previous_ha_open else "red"
            
            return {
                'current_ha_close': current_ha_close,
                'current_ha_open': current_ha_open,
                'previous_ha_close': previous_ha_close,
                'previous_ha_open': previous_ha_open,
                'ha_color': ha_color,
                'prev_ha_color': prev_ha_color
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating Heikin-Ashi: {str(e)}")
            return {}

    def _calculate_take_profit_levels(self, entry_price: float, forecast_price: float, stop_loss = None, signal: str = '') -> Dict:
        """
        Implement two-step take profit system:
        1. First TP: min(stop_loss_distance, forecast_distance)
        2. If forecast closer: exit full position
        3. If not closer: exit 50%, move SL to breakeven
        4. Second TP: remaining 50% at forecast price
        """
        try:
            if not forecast_price or not stop_loss:
                return {
                    'first_tp': None,
                    'first_tp_percentage': 100,
                    'second_tp': None,
                    'move_sl_to_breakeven': False,
                    'strategy': 'single_exit'
                }

            # Calculate distances
            stop_loss_distance = abs(entry_price - stop_loss)
            forecast_distance = abs(forecast_price - entry_price)

            # Determine direction
            if signal == 'BUY':
                # For long positions
                if forecast_price > entry_price:  # Bullish forecast
                    if forecast_distance <= stop_loss_distance:
                        # Forecast is closer - single exit at forecast
                        return {
                            'first_tp': forecast_price,
                            'first_tp_percentage': 100,
                            'second_tp': None,
                            'move_sl_to_breakeven': False,
                            'strategy': 'single_exit_forecast',
                            'reason': 'Forecast closer than stop loss distance'
                        }
                    else:
                        # Stop loss distance is closer - two-step exit
                        first_tp = entry_price + stop_loss_distance
                        breakeven_price = entry_price + (entry_price * 0.0002)  # +0.02%
                        return {
                            'first_tp': first_tp,
                            'first_tp_percentage': 50,
                            'second_tp': forecast_price,
                            'move_sl_to_breakeven': True,
                            'breakeven_price': breakeven_price,
                            'strategy': 'two_step_exit',
                            'reason': 'Stop loss distance closer, using two-step approach'
                        }
                else:
                    # Bearish forecast for long position - use stop loss distance
                    first_tp = entry_price + stop_loss_distance
                    return {
                        'first_tp': first_tp,
                        'first_tp_percentage': 100,
                        'second_tp': None,
                        'move_sl_to_breakeven': False,
                        'strategy': 'single_exit_sl_distance',
                        'reason': 'Bearish forecast, using stop loss distance'
                    }

            elif signal == 'SELL':
                # For short positions
                if forecast_price < entry_price:  # Bearish forecast
                    if forecast_distance <= stop_loss_distance:
                        # Forecast is closer - single exit at forecast
                        return {
                            'first_tp': forecast_price,
                            'first_tp_percentage': 100,
                            'second_tp': None,
                            'move_sl_to_breakeven': False,
                            'strategy': 'single_exit_forecast',
                            'reason': 'Forecast closer than stop loss distance'
                        }
                    else:
                        # Stop loss distance is closer - two-step exit
                        first_tp = entry_price - stop_loss_distance
                        breakeven_price = entry_price - (entry_price * 0.0002)  # -0.02%
                        return {
                            'first_tp': first_tp,
                            'first_tp_percentage': 50,
                            'second_tp': forecast_price,
                            'move_sl_to_breakeven': True,
                            'breakeven_price': breakeven_price,
                            'strategy': 'two_step_exit',
                            'reason': 'Stop loss distance closer, using two-step approach'
                        }
                else:
                    # Bullish forecast for short position - use stop loss distance
                    first_tp = entry_price - stop_loss_distance
                    return {
                        'first_tp': first_tp,
                        'first_tp_percentage': 100,
                        'second_tp': None,
                        'move_sl_to_breakeven': False,
                        'strategy': 'single_exit_sl_distance',
                        'reason': 'Bullish forecast, using stop loss distance'
                    }

            # Default fallback
            return {
                'first_tp': None,
                'first_tp_percentage': 100,
                'second_tp': None,
                'move_sl_to_breakeven': False,
                'strategy': 'no_strategy'
            }

        except Exception as e:
            self.logger.error(f"Error calculating take profit levels: {str(e)}")
            return {
                'first_tp': None,
                'first_tp_percentage': 100,
                'second_tp': None,
                'move_sl_to_breakeven': False,
                'strategy': 'error',
                'error': str(e)
            }
    
    def _analyze_trading_conditions(self, market_data: pd.DataFrame, forecast, swing_points: Dict,
                                  heikin_ashi: Dict, highest_price: float, lowest_price: float) -> Dict:
        """Analyze trading conditions and generate signals (extracted from trade.py check_trading)"""
        try:
            if market_data is None or len(market_data) == 0:
                return {'error': 'Market data is None or empty'}

            if 'close' not in market_data.columns:
                return {'error': 'Close price data not available'}

            current_price = float(market_data['close'].iloc[-1])

            # Calculate SMA12
            market_data['SMA12'] = market_data['close'].astype(float).rolling(window=12).mean()
            current_sma12 = float(market_data['SMA12'].iloc[-1])

            # Calculate potential moves
            potential_up_move = (highest_price - current_price) / current_price if current_price > 0 else 0
            potential_down_move = (current_price - lowest_price) / current_price if current_price > 0 else 0
            
            # Extract values
            last_swing_high = swing_points.get('swing_high')
            last_swing_low = swing_points.get('swing_low')
            ha_color = heikin_ashi.get('ha_color')
            prev_ha_color = heikin_ashi.get('prev_ha_color')
            previous_ha_close = heikin_ashi.get('previous_ha_close')
            
            signal = 'HOLD'
            confidence = 99.95
            entry_price = current_price
            stop_loss = None
            take_profit = None
            side = None
            order_type = 'MARKET'
            quantity = None
            
            # Enhanced trading conditions matching trade.py exactly (lines 568-616)
            if (last_swing_high is not None and last_swing_high > current_price and
                potential_down_move > 0.01 and  # At least 1% potential down move
                ha_color == "red" and
                prev_ha_color == "red" and
                previous_ha_close is not None and previous_ha_close < current_sma12):
                
                signal = 'SELL'
                side = 'SELL'
                confidence = min(potential_down_move * 100, 90)  # Cap at 90%
                stop_loss = round(last_swing_high * 1.003, 4) if last_swing_high is not None else None

                # Enhanced profit-taking logic
                tp_levels = self._calculate_take_profit_levels(
                    entry_price=current_price,
                    forecast_price=lowest_price,
                    stop_loss=stop_loss,
                    signal='SELL'
                )

                take_profit = tp_levels.get('first_tp')
                quantity = max(round((1000 / current_price), 4), 0.002)  # Min 0.002 BTC
                
                self.logger.info(f"SELL Signal Generated - Potential Down Move: {potential_down_move*100:.2f}%")
                self.logger.info(f"SELL Signal Details - Swing High: {last_swing_high}, Stop Loss: {stop_loss}, Take Profit: {take_profit}")
            
            # BUY Signal Conditions (from trade.py lines 593-616)
            elif (last_swing_low is not None and last_swing_low < current_price and
                  potential_up_move > 0.01 and  # At least 1% potential up move
                  ha_color == "green" and
                  prev_ha_color == "green" and
                  previous_ha_close is not None and previous_ha_close > current_sma12):
                
                signal = 'BUY'
                side = 'BUY'
                confidence = min(potential_up_move * 100, 90)  # Cap at 90%
                stop_loss = round(last_swing_low / 1.003, 4) if last_swing_low is not None else None

                # Enhanced profit-taking logic
                tp_levels = self._calculate_take_profit_levels(
                    entry_price=current_price,
                    forecast_price=highest_price,
                    stop_loss=stop_loss,
                    signal='BUY'
                )

                take_profit = tp_levels.get('first_tp')
                quantity = max(round((1000 / current_price), 4), 0.002)  # Min 0.002 BTC
                
                self.logger.info(f"BUY Signal Generated - Potential Up Move: {potential_up_move*100:.2f}%")
                self.logger.info(f"BUY Signal Details - Swing Low: {last_swing_low}, Stop Loss: {stop_loss}, Take Profit: {take_profit}")
            
            # Get profit-taking strategy information
            tp_strategy = {}
            if signal in ['BUY', 'SELL']:
                if signal == 'BUY':
                    tp_strategy = self._calculate_take_profit_levels(
                        entry_price=current_price,
                        forecast_price=highest_price,
                        stop_loss=stop_loss,
                        signal='BUY'
                    )
                else:  # SELL
                    tp_strategy = self._calculate_take_profit_levels(
                        entry_price=current_price,
                        forecast_price=lowest_price,
                        stop_loss=stop_loss,
                        signal='SELL'
                    )

            # Calculate risk-reward ratio
            risk_reward_ratio = 0
            risk = 0
            reward = 0

            if stop_loss and take_profit:
                if signal == 'BUY':
                    risk = abs(entry_price - stop_loss)
                    reward = abs(take_profit - entry_price)
                elif signal == 'SELL':
                    risk = abs(stop_loss - entry_price)
                    reward = abs(entry_price - take_profit)

                if risk > 0:
                    risk_reward_ratio = reward / risk
            
            # Return comprehensive trading signal
            return {
                'signal': signal,
                'side': side,
                'action': 'OPEN' if signal != 'HOLD' else 'HOLD',
                'confidence': round(confidence, 2),
                'entry_price': entry_price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'quantity': quantity,
                'order_type': order_type,
                'risk_reward_ratio': round(risk_reward_ratio, 2) if risk_reward_ratio else None,
                'potential_up_move': round(potential_up_move * 100, 2),
                'potential_down_move': round(potential_down_move * 100, 2),
                'current_price': current_price,
                'swing_high': last_swing_high,
                'swing_low': last_swing_low,
                'ha_color': ha_color,
                'prev_ha_color': prev_ha_color,
                'sma12': current_sma12,
                'market_conditions': {
                    'trend': ha_color,
                    'momentum': prev_ha_color,
                    'swing_analysis': {
                        'resistance': last_swing_high,
                        'support': last_swing_low
                    }
                },
                'forecast_data': {
                    'highest_predicted': highest_price,
                    'lowest_predicted': lowest_price
                },
                'profit_taking_strategy': tp_strategy,
                'timestamp': datetime.utcnow().isoformat(),
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing trading conditions: {str(e)}")
            return {'error': str(e)}